<template>
  <el-container class="container-manage-sg" direction="vertical">
    <el-header>
      <el-card>
        <el-input
            v-model="selectData.fuzzyValue"
            class="filter_input"
            clearable
            placeholder="检索字段（合同编号、资金单据编号）"
            @clear="reloadPerformResultData"
            @keyup.enter.native="reloadPerformResultData"
        >
          <el-popover slot="prepend" placement="bottom-start" trigger="click" width="1000">
            <el-form label-width="100px" size="mini">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="合同编码">
                    <el-input v-model="selectData.sq_contractCode" clearable placeholder="请输入..."/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="合同名称">
                    <el-input v-model="selectData.sq_contractName" clearable placeholder="请输入..."/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="对方签约主体">
                    <el-input v-model="selectData.sq_otherPartyName" clearable placeholder="请输入..."/>
                  </el-form-item>
                </el-col>

              </el-row>
              <div style="float: right">
                <el-button icon="el-icon-search" size="mini" type="primary" @click="search_">搜索</el-button>
                <el-button icon="el-icon-refresh-left" size="mini" type="primary" @click="empty_">重置</el-button>
              </div>
            </el-form>
            <el-button slot="reference" size="mini" type="primary">高级检索</el-button>
          </el-popover>
          <el-button slot="append" icon="el-icon-search" @click="search_"/>
        </el-input>
      </el-card>
    </el-header>
    <el-main>
      <SimpleBoardIndex :hasAdd="true" style="height:40% !important" title="待认领列表">
        <el-table
            ref="table"
            v-loading="performLoading"
            :data="performResultTableData"
            border
            class="indexTable"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            element-loading-spinner="el-icon-loading"
            element-loading-text="加载中..."
            fit
            height="25vh"
            highlight-current-row
            stripe
            style="width: 100%;"
            @row-click="rowClick"
            @sort-change="tableSort"
        >
          <el-table-column align="center" fixed="left" label="序号" type="index" width="50"></el-table-column>
          <el-table-column align="center"  label="我方签约主体" prop="ourPartyName" show-overflow-tooltip
                           width="150"></el-table-column>
          <el-table-column align="center" label="对方签约主体" min-width="150" prop="otherPartyName"
                           show-overflow-tooltip></el-table-column>
          <el-table-column align="center"  label="合同编号" prop="contractCode" show-overflow-tooltip
                           width="150"></el-table-column>
          <el-table-column align="center" label="收支方向" prop="revenueExpenditure" width="150"></el-table-column>
          <el-table-column align="center" label="结算方式" prop="settlementMethod" width="150"></el-table-column>
          <el-table-column align="center" label="币种" prop="currency" width="100"></el-table-column>
          <el-table-column align="center" label="汇率" prop="exchangeRate" width="80"></el-table-column>
          <el-table-column align="center" label="实际履行金额" prop="actualAmount" width="150"></el-table-column>
          <el-table-column align="center" label="实际履行日期" prop="actualPerformDate" width="150">
            <template slot-scope="scope">
              <span>{{ scope.row.actualPerformDate | parseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="资金单据编号" prop="fundDocumentCode" width="150"></el-table-column>
          <el-table-column align="center" label="实际履行金额（人民币）" prop="actualAmountRmb"
                           width="150"></el-table-column>

        </el-table>
      </SimpleBoardIndex>
      <el-footer>
        <pagination
            :limit.sync="selectData.limit"
            :page.sync="selectData.page"
            :total="selectData.total"
            @pagination="reloadPerformResultData"
        />
      </el-footer>
      <SimpleBoardIndex :hasAdd="true" style="height:50% !important" title="履行计划">
        <template slot='button'>
          <el-button size="mini" type="primary" @click="cliamResult">确定</el-button>
        </template>
        <el-table
            ref="table"
            :data="tableData"
            :row-style="getClass"
            border
            class="indexTable"
            fit
            :height="table_height"
            highlight-current-row

            stripe
            style="width: 100%;">
          <el-table-column align="center" label="序号" type="index" width="50"></el-table-column>
          <el-table-column align="center" label="计划编号" prop="performCode" show-overflow-tooltip
                           width="150"></el-table-column>
          <el-table-column align="center" label="履行事项" prop="performText" show-overflow-tooltip
                           width="300"></el-table-column>
          <el-table-column align="center" label="合同条款" prop="contractTerms" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="项目编号" prop="projectCode" width="150"></el-table-column>
          <el-table-column align="center" label="收支方向" prop="revenueExpenditure" width="150"></el-table-column>
          <el-table-column align="center" label="计划履行金额" prop="planAmount" width="100"></el-table-column>
          <el-table-column align="center" label="计划履行金额（人民币）" prop="planAmountRmb"
                           width="200"></el-table-column>
          <el-table-column align="center" label="待履行金额" prop="performExecutoryMoney" width="200"></el-table-column>
          <el-table-column align="center" label="履行进度" width="100">
            <template slot-scope="scope">
              <span
                  v-if="scope.row.performProgress!='' && scope.row.performProgress!=null">{{ scope.row.performProgress }}%</span>
              <span v-else>{{ 0 }}%</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="认领金额" width="200">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.claimAmount" :controls="false"></el-input-number>
            </template>
          </el-table-column>

        </el-table>
      </SimpleBoardIndex>


      <el-dialog
          :close-on-click-modal="false"
          :visible.sync="orgVisible"
          append-to-body
          title="选择人员"
          width="50%">
        <div class="el-dialog-div">
          <orgTree :accordion="false" :checked-data.sync="zxcheckedData" :is-check="is_Check" :is-checked-user="isCheckedUser"
                   :is-filter="true" :is-not-cascade="true" :show-user="showUser"/>
        </div>
        <span slot="footer" class="dialog-footer">
        <el-button class="negative-btn" icon="" @click="cancelDept_">取消</el-button>
        <el-button class="active-btn" type="primary" @click="choiceDeptSure_">确定</el-button>
          </span>
      </el-dialog>

    </el-main>

  </el-container>
</template>

<script>
import {mapGetters} from "vuex"
import contractApi from '@/api/contract/contract'
import noticeApi from '@/api/_system/notice'
import bmContractPerformResultApi from '@/api/contract/bmContractPerformResult';
import bmContractPerformApi from '@/api/contract/bmContractPerform';

import pagination from '../../../components/Pagination/PaginationIndex'
import SimpleBoardIndex from "@/view/components/SimpleBoard/SimpleBoardIndex"

export default {
  name: 'PerformResultClaimIndex',
  inject: ['layout'],
  computed: {
    ...mapGetters(['orgContext', 'currentFunctionId']),
    isView: function () {
      return this.dataState === this.utils.formState.VIEW
    }
  },
  components: {pagination, SimpleBoardIndex},
  watch: {
    /*contractTableData:function(){
        this.$nextTick(function () {
            this.$refs.table.setCurrentRow(this.contractTableData[this.index])

        })
    }*/
  },
  data() {
    return {
      performLoading: false,
      elsePlanAmount: 0,//剩余计划金额
      totalPlanAmount: 0,//计划总金额

      performRowData: {},
      performShow: false,
      tempData: {
        tags: [],
        timeAxis: []
      },
      table: null,
      warnData: [
        {
          id: this.utils.createUUID(),
          warnType: '合同到期提醒',
          warn: false,
          warnPsnName: null,
          warnPsnId: null,
          warnTime: null,
          relationId: null,
          warnTimeName: null
        },
        {
          id: this.utils.createUUID(),
          warnType: '合同计划到期提醒',
          warn: false,
          warnPsnName: null,
          warnPsnId: null,
          warnTime: null,
          relationId: null,
          warnTimeName: null
        }
      ],
      revenueDetailData: [],
      tableTempData: [],
      table_height: null, // 定义表格高度
      tableData: [], // 定义表格数据源
      performResultTableData: [], // 合同列表
      warnDialog: false,
      revenueDetailDialog: false,
      showUser: false,
      is_Check: false,
      isCheckedUser: false,
      orgVisible: false,
      zxcheckedData: [],
      warnIndex: 0,
      index: 0,
      planTextData1: [],
      planTextData: [],
      hasText: true,
      drawerDialog: false,
      performDialog: false,
      isShow: false,
      dataState: "edit",
      isResouceShow: 0,
      moneyWayName: null,
      nuit: null,
      relationId: null,
      money: null,
      moneyType: null,
      planId: null,
      chart: null,
      performId: null,
      performMoney: '0',
      performEndTime: null, // 履行结束时间
      startTime: null, // 履行开始时间
      state: null,
      isFirst: true,
      tableData1: null, // 履行数据源
      timeData: [{"id": "0", "dicName": "当天（多选）"}, {"id": "7", "dicName": "提前7天"}, {
        "id": "30",
        "dicName": "提前30天"
      }, {"id": "90", "dicName": "提前90天"}, {"id": "180", "dicName": "提前180天"}],
      typeData: [
        {"id": "1", "dicName": "未履行", "color": "#82848a"},
        {"id": "3", "dicName": "履行中", "color": "#45b97c"},
        {"id": "4", "dicName": "异常履行", "color": "#f47920"},
        {"id": "5", "dicName": "履行完成", "color": "#45b97c"},
        {"id": "6", "dicName": "异常关闭", "color": "#82848a"}],
      selectData: {
        sq_cliamFlag: '0',
        page: 1,
        limit: 10,
        total: 0,
      },
      perform: {
        id: null,
        contractName: null, // 合同名称
        performName: null, // 履行标题
        performMain: null, // 履行主体
        performMainId: null, // 履行主体id
        performTask: null, // 履行任务
        performRemark: null, // 履行说明
        performStartTime: null, // 开始时间
        performEndTime: null, // 结束时间
        performStatus: null, // 合同履行状态
        isPostpone: null, // 是否逾期
        relationId: null, // 关联合同id
        createOgnName: null, //  经办单位
        createDeptName: null, // 经办部门
        createPsnName: null, // 经办人
        createTime: null, // 经办时间
        createOgnId: null,
        createDeptId: null,
        createPsnId: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createGroupId: null,
        createGroupName: null,
        createOrgId: null,
        createOrgName: null,
        page: 1,
        limit: 10,
        total: 0,
        sortName: null,
        order: null
      },
      contractId: null,
      currentRow: {}
    }
  },
  activated() {
    this.reloadPerformResultData()
  },
  created() {
    //      this.refreshData()
    this.reloadPerformResultData();
    if (this.$route.query.read === 'false') {
      noticeApi.read({sid: this.$route.query.sid})
    }
  },
  mounted() {
    this.$nextTick(function () {
      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 45
      const self = this
      window.onresize = function () {
        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 45
      }
    })
  },
  methods: {
    //加载已生效的合同数据
    reloadPerformResultData() {
      this.selectData.orgId = this.orgContext.currentOrgId
      bmContractPerformResultApi.query(this.selectData).then(res => {
        this.performResultTableData = res.data.data.records
        if (this.performResultTableData.length > 0) {
          this.currentRow = this.performResultTableData[0];
          this.loaddingPerformData(this.currentRow.contractCode, this.currentRow);
        }
        this.selectData.total = res.data.data.total
      })
    },
    /*contractMoneyFormat(row, column, cellValue, index) {
        if (row.moneyTypeName === '总价') {
            return row.contractMoney
        } else {
            return row.contractMoneyString
        }
    },*/
    tableSort(column, prop, order) {
      this.selectData.sortName = column.prop
      this.selectData.order = column.order === "ascending"
      this.reloadPerformResultData()
    },
    search_() { // 查询
      this.reloadPerformResultData()
    },
    empty_() {
      this.selectData = {
        sq_contractName: '', // 合同名称
        sq_otherPartyName: null, // 对方签约主体
        page: 1,
        limit: 10,
        total: 0,
        sortName: null,
        order: false,
        orgId: this.orgContext.currentOgnId,
        sq_cliamFlag: '0'
      }
      this.reloadPerformResultData()
    },
    refresh_() {
      this.empty();
      //                this.reloadPerformResultData()
    },


    loaddingPerformData(contractCode, row) {
      this.performLoading = true;
      bmContractPerformApi.queryByContractCode({contractCode: contractCode}).then(res => {
        this.tableData = res.data.data;
        this.performLoading = false;
        /*let totPlamount = 0;
        let totalAmount = row.afterChangeMoney==''?row.contractAmount:row.afterChangeMoney;
        this.tableData.forEach(item => {
            if(item.planAmount != null && item.planAmount != ''){
                totPlamount += item.planAmount;
            }
        })
        this.totalPlanAmount = totPlamount;
        this.elsePlanAmount = totalAmount - totPlamount;*/
      });

    },
    rowClick(row) {
      this.currentRow = row;
      this.loaddingPerformData(row.contractCode, row);
    },
    changeStatus($event, selectedRef) {
      console.log($event)
      const color = this.getColor($event)
      // 改变下拉框颜色值
      this.$refs[selectedRef].$el.children[0].children[0].style.color = '' + color + ''
      contractApi.updateContractPerformState({id: selectedRef, stateName: $event}).then(response => {
        this.updateData(selectedRef);
      })
    },
    loadtatus($event, selectedRef) {
      const color = this.getColor($event)
      // 改变下拉框颜色值
      this.$refs[selectedRef].$el.children[0].children[0].style.color = '' + color + ''
    },
    getSytle(dicName) {
      if (dicName !== null) {
        for (let i = 0; i, this.typeData.length; i++) {
          if (this.typeData[i].dicName == dicName) {
            return "color:" + this.typeData[i].color
          }
        }
      }
      return "color:#82848a"
    },
    getColor(dicName) {
      if (dicName !== null) {
        for (let i = 0; i, this.typeData.length; i++) {
          if (this.typeData[i].dicName == dicName) {
            return this.typeData[i].color
          }
        }
      }
      return "#82848a"
    },
    updatePlanData(val) {
      this.planTextData = val
    },
    updateData(val) {
      contractApi.queryContractApprovalById({id: val}).then(response => {
        var row = response.data.data.contractApproval
        this.index = this.tableData.findIndex(item => item.id === val)
        this.$set(this.tableData, this.index, row)
        this.rowClick(row);
      })

    },
    updatePlanData1(val) {
      //this.rowClick(this.currentRow)
      this.planTextData1 = val
    },
    // 格式化日期
    dateFormat(cellValue) {
      if (cellValue != null && cellValue !== '') {
        var currentDate = new Date(cellValue)
        var year = currentDate.getFullYear()
        var month = (currentDate.getMonth() + 1) + ''
        var day = (currentDate.getDate()) + ''
        if (month.length === 1) {
          month = '0' + month
        }
        if (day.length === 1) {
          day = '0' + day
        }
        var formatDate1 = year + '-' + month + '-' + day
        return formatDate1
      }
    },

    getClass(row) {
      //判断是否一个月未进行数据修改
      var starDate = row.row.updateTime;
      var date = new Date();
      var sDate = new Date(starDate).getTime();
      var eDate = date.getTime();
      //将当前月份加1，下移到下一个月
      date.setMonth(date.getMonth() + 1);
      //将当前的日期置为0，
      date.setDate(0);
      var thisMothDays = 1000 * 3600 * 24 * date.getDate();
      if ((eDate - sDate > thisMothDays) && row.row.performState !== "逾期履行完成" && row.row.performState !== "正常履行完成" && row.row.performState !== "合同关闭") {
        return {"background-color": "#ff494954"}
      } else {
        return ""
      }
    },

    cancelDept_() {
      this.orgVisible = false
    },
    choiceDeptSure_() {
      if (this.zxcheckedData) {
        for (let i = 0; i < this.zxcheckedData.length; i++) {
          const item = this.zxcheckedData[i];
          if (i === 0) {
            this.warnData[this.warnIndex].warnPsnName = item.name
            this.warnData[this.warnIndex].warnPsnId = item.unitId
          } else {
            this.warnData[this.warnIndex].warnPsnName += '、' + item.name
            this.warnData[this.warnIndex].warnPsnId += ',' + item.unitId
          }
        }
        this.orgVisible = false
      } else {
        this.warnData[this.warnIndex].warnPsnId = null
        this.warnData[this.warnIndex].warnPsnName = null
        this.orgVisible = false
      }
    },
    cliamResult() {
      let totalAmount = 0;
      let performArr = [];
      let flag = true;
      this.tableData.forEach(item => {
        if (item.claimAmount > item.performExecutoryMoney) {
          this.$message.warning('您输入的内容超过待履行金额，请调整;');
          flag = false;
          item.claimAmount = null;
          return;
        }
        if (item.claimAmount) {
          let obj = {performId: item.performId, claimAmount: item.claimAmount, id: this.currentRow.id};
          performArr.push(obj);
          totalAmount += item.claimAmount;
        }
      })
      if (flag) {
        if (totalAmount == this.currentRow.actualAmount) {
          this.$confirm('确认提交吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let performStr = JSON.stringify(performArr);
            bmContractPerformResultApi.cliamResult({performStr: performArr, resultId: this.currentRow.id}).then(res => {
              if (res.status === 200) {
                this.$message.success('提交成功');
                this.reloadPerformResultData();
                this.tableData = [];
              }
            })
          });
        } else {
          this.$message.warning('履行金额未分配完成！请调整');
        }
      }
    }


  }
}
</script>
<style scoped>
.snap-list .el-timeline {
  padding-left: 130px;
  width: 70%;
}

/deep/ .left-font {
  font-size: 16px;
  color: #1890FF;
  font-weight: bold
}

/deep/ .el-timeline-item-left-6 .el-timeline-item__wrapper {
  left: -260px;

}

/deep/ .el-timeline-item-left-4 .el-timeline-item__wrapper {
  left: 2px;
}

/deep/ .el-timeline-item__node--large {
  /* border:solid 3px #dfe4ed;*/
  left: -4px;
  width: 17px;
  height: 17px;
}

.case-drawer {
  width: 800px !important;
}

.case-drawer .el-drawer__header {
  margin-bottom: 0;
}

.case-drawer .el-scrollbar__wrap {
  overflow-x: hidden;
}

.spaceBetween {
  color: #82848a;
  padding-top: 4px;
}


::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background-color: #e4e4e4;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: #a1a3a9;
  border-radius: 6px;
}

/deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3px;
}


.filter_input .el-input-group__prepend {
  color: #FFFFFF;
  background-color: #1890ff;
  border-color: #1890ff;
}


input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}

.myNew .el-input__inner {
  background: #fff;
  height: 32px;
  border: 1px solid;
  border-color: #3E7BFA;
  line-height: 32px;
  color: #3E7BFA
}

.myNew .el-input__suffix {
  color: #3E7BFA;
}

.myNew input::-webkit-input-placeholder {
  color: #3E7BFA;
}


.step {
  position: relative;
  display: flex;
  font-size: 0;
}

.left {
  flex-grow: 0;
  position: relative;
  display: inline-block;
  text-align: center;
  font-weight: bold;
  width: 70%;
  font-size: 12px;
  line-height: 20px;
  height: 20px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  border-width: 1px;
  border-style: solid;
}

.left1 {
  flex-grow: 0;
  position: relative;
  display: inline-block;
  color: #333;
  text-align: center;
  font-weight: bold;
  width: 70%;
  font-size: 12px;
  line-height: 20px;
  height: 20px;
  border-radius: 5px;
  border-width: 1px;
  border-style: solid;
}

.right1 {
  flex-grow: 1;
  position: relative;
  display: inline-block;
  /* width:30%; */
  background: #fff;
  color: #333;
  font-weight: bold;
  text-align: center;
  font-size: 12px;
  line-height: 20px;
  height: 20px;
  text-align: center;
  border-radius: 5px;
  border-width: 1px;
  border-style: solid;
}

.right {
  flex-grow: 1;
  position: relative;
  display: inline-block;
  /* width:30%; */
  background: #fff;
  color: #333;
  font-weight: bold;
  text-align: center;
  font-size: 12px;
  line-height: 20px;
  height: 20px;
  text-align: center;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  border-width: 1px;
  border-style: solid;
}

.tip-arrow {
  position: absolute;
  font-size: 11px;
  left: 38%;
  display: inline-block;
  width: 7px;
  height: 7px;
  z-index: 10;
}

.bar-tip-box {
  position: absolute;
  top: -5px;
  right: 50%;
  transform: translate(50%, -100%);
  text-align: left;
  padding: 5px 10px;
  width: max-content;
  color: #fff;
  font-size: 12px;
  font-weight: 400;
  border-radius: 3px;
  background-color: #fff;
  z-index: 10;
}


.bar-tip-box p {
  margin: 0;
  padding-bottom: 5px;
}
</style>
