export const TzxmfxIndex = () => import('../Compliance/Risk/tzxmfx/TzxmfxIndex.vue');
export const RiskWaringIndex = () => import('../Compliance/RiskWaringIndex.vue');
export const RiskWaringEventBook = () => import('../Compliance/Risk/ComplianceRiskearlyWarning/RiskWaringEventBook.vue');

export const WgjbIndex = () => import('../Compliance/Management/ComplianceOperation/WgjbIndex.vue');
export const WgjbgsIndex = () => import('../Compliance/Management/ComplianceOperation/WgjbgsIndex.vue');
export const HgbgIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/Hgbg/HgbgIndex.vue');

export const HgbgMain = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/Hgbg/HgbgMain.vue');
export const HgscIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgscIndex.vue');
export const PublishCheckIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/PublishCheckIndex.vue');
export const HgzxjhMain = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgzxjhMain.vue');
export const ProjectDemonstrationMain = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/ProjectDemonstrationMain.vue');
export const HgyxxpjMain = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgyxxpjMain.vue');
export const RectificationPlanIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/RectificationPlanIndex.vue');
export const AuditTrailIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/AuditTrailIndex.vue');
export const ResultsFeedbackIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/ResultsFeedbackIndex.vue');
export const PublishCheckDetail = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/PublishCheckDetail.vue');
export const CheckResultDetail = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/CheckResultDetail.vue');
export const RectificationPlanDetail = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/RectificationPlanDetail.vue');
export const AuditTrailDetail = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/AuditTrailDetail.vue');
export const ResultFeedbackDetail = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/ResultsFeedbackDetail.vue');
export const SelectedCheckIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/SelectedCheckIndex.vue');
export const CheckResultIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/CheckResultIndex.vue');
export const CheckResultMain = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/CheckResultMain.vue');
export const HgrykIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgrykIndex.vue');
export const HgrykIndexRegister = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgrykIndexRegister.vue');
export const HgryDetail = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgryDetail.vue');
export const HgpxxxglIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgpxxxglIndex.vue');
export const HgpxxxyhIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgpxxxyhIndex.vue');
export const HggfglIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HggfglIndex.vue');
export const HgzzjsIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgzzjsIndex.vue');

export const HgbgMainDetail = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/Hgbg/HgbgMainDetail.vue');
export const HgscMainDetail = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgscMainDetail.vue');

export const HgscMainDetailHfhg = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgscMainDetailHfhg.vue');

export const HgscMainDetailQsss = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgscMainDetailQsss.vue');

export const HgscMainDetailTsjy = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgscMainDetailTsjy.vue');

export const HgscMainDetailZdhf = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgscMainDetailZdhf.vue');

export const HgpxxxglMainDetail = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgpxxxglMainDetail.vue');
export const HgpxxxglDetail = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgpxxxglDetail.vue');
export const HgpxxxyhDetail = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgpxxxyhDetail.vue');
export const HggfglMainDetail = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HggfglMainDetail.vue');

export const HgpjIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceEvaluation/HgpjIndex.vue');

export const HgpjMainDetail = () => import('../Compliance/Management/ComplianceOperation/ComplianceEvaluation/HgpjMainDetail.vue');
export const HgwzIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceAccountability/HgwzIndex.vue');
export const HgwzMainDetail = () => import('../Compliance/Management/ComplianceOperation/ComplianceAccountability/HgwzMainDetail.vue');
export const HgwzMain = () => import('../Compliance/Management/ComplianceOperation/ComplianceAccountability/HgwzMain.vue');
export const XtjdMain = () => import('../Compliance/Management/ComplianceOperation/ComplianceAccountability/XtjdMain.vue');
export const TzxmfxMainDetail = () => import('../Compliance/Risk/tzxmfx/TzxmfxMainDetail.vue');
export const TzxmfxMain = () => import('../Compliance/Risk/tzxmfx/TzxmfxMain.vue');
export const RiskWaringMainApproval = () => import('../Compliance/child/RiskWaringMainApproval.vue');

export const HgscMain = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgscMain.vue');

export const HgscMainHfhg = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgscMainHfhg.vue');

export const HgscMainQsss = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgscMainQsss.vue');

export const HgscMainTsjy = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgscMainTsjy.vue');

export const HgscMainZdhf = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgscMainZdhf.vue');

export const RiskWaringMain = () => import('../Compliance/child/RiskWaringMain.vue');

export const CompolianceRiskWarning = () => import('../Compliance/Risk/ComplianceRiskearlyWarning/CompolianceRiskWarning.vue');

export const ComplianceRiskWarningDetial = () => import('../Compliance/Risk/ComplianceRiskearlyWarning/ComplianceRiskWarningDetial.vue');

export const CompolianceRiskWarningMain = () => import('../Compliance/Risk/ComplianceRiskearlyWarning/CompolianceRiskWarningMain.vue');

export const RiskWarningDistribution = () => import('../Compliance/Risk/ComplianceRiskearlyWarning/RiskWarningDistribution.vue');

export const RiskWarningReceive = () => import('../Compliance/Risk/ComplianceRiskearlyWarning/RiskWarningReceive.vue');

export const RiskWarningDataBase = () => import('../Compliance/Risk/ComplianceRiskearlyWarning/RiskWarningDataBase.vue');
export const RiskdataLedgerIndex = () => import('../Compliance/Risk/ComplianceRiskearlyWarning/RiskdataLedgerIndex.vue');

export const RiskWarningStandingBook = () => import('../Compliance/Risk/ComplianceRiskearlyWarning/RiskWarningStandingBook.vue');

export const HgwhxcIndex = () => import('../Compliance/CulturalManagement/CulturePropaganda/HgwhxcIndex.vue');
export const CjwttzIndex = () => import('../Compliance/CommonQuestion/CjwttzIndex.vue');
export const CjwtallIndex = () => import('../Compliance/CommonQuestion/CjwtallIndex.vue');
export const CjwttzMainDetail = () => import('../Compliance/CommonQuestion/CjwttzMainDetail.vue');
export const HgwhxcMainDetail = () => import('../Compliance/CulturalManagement/CulturePropaganda/HgwhxcMainDetail.vue');
export const HgfxsbIndex = () => import('../Compliance/Basic/ComplianceRiskIdentification/HgfxsbIndex.vue');
export const HgfxsbLedgerIndex = () => import('../Compliance/Basic/ComplianceRiskIdentification/HgfxsbLedgerIndex.vue');
export const hgfxsbMainDetail = () => import('../Compliance/Basic/ComplianceRiskIdentification/HgfxsbMainDetail.vue');

export const HgalkIndex = () => import('../Compliance/CulturalManagement/ComplianceCaseLibrary/HgalkIndex.vue');

export const HgalkMainDetail = () => import('../Compliance/CulturalManagement/ComplianceCaseLibrary/HgalkMainDetail.vue');

export const ZcfgkIndex = () => import('../Compliance/CulturalManagement/ComplianceRegulationsLibrary/ZcfgkIndex.vue');

export const ZcfgkMainDetail = () => import('../Compliance/CulturalManagement/ComplianceRegulationsLibrary/ZcfgkMainDetail.vue');
export const GwzzqdIndex = () => import('../Compliance/Basic/JobResponsibilitiesList/GwzzqdIndex.vue');
export const GwzzqdLedgerIndex = () => import('../Compliance/Basic/JobResponsibilitiesList/GwzzqdLedgerIndex.vue');
export const GwzzqdMainDetail = () => import('../Compliance/Basic/JobResponsibilitiesList/GwzzqdMainDetail.vue');
export const LcgkqdIndex = () => import('../Compliance/Basic/ProcessControlList/LcgkqdIndex.vue');
export const LcgkqdLedgerIndex = () => import('../Compliance/Basic/ProcessControlList/LcgkqdLedgerIndex.vue');
export const LcgkqdMainDetail = () => import('../Compliance/Basic/ProcessControlList/LcgkqMainDetail.vue');

export const FxgcspIndex = () => import('../Compliance/Risk/fxgcsp/FxgcspIndex.vue');

export const FxgcspMainDetail = () => import('../Compliance/Risk/fxgcsp/FxgcspMainDetail.vue');
export const FxgcspMain = () => import('../Compliance/Risk/fxgcsp/FxgcspMain.vue');

export const RiskApprovalProcessIndex = () => import('../Compliance/Risk/RiskApprovalProcess/RiskApprovalProcessIndex.vue');

export const RiskApprovalProcessMainDetail = () => import('../Compliance/Risk/RiskApprovalProcess/RiskApprovalProcessMainDetail.vue');
export const RiskApprovalProcessMain = () => import('../Compliance/Risk/RiskApprovalProcess/RiskApprovalProcessMain.vue');
export const ProjectDemonstrationIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/ProjectDemonstrationIndex.vue');
export const ProjectDemonstrationDetail = () =>
	import('../Compliance/Management/ComplianceOperation/ComplianceReport/ProjectDemonstrationDetail.vue');

export const HgpjtzIndex = () => import('../Compliance/Management/ComplianceOperation/ComplianceEvaluationNotificationSlip/HgpjtzIndex.vue');

export const HgpjtzMainDetail = () =>
	import('../Compliance/Management/ComplianceOperation/ComplianceEvaluationNotificationSlip/HgpjtzMainDetail.vue');

export const HgpjtzMain = () => import('../Compliance/Management/ComplianceOperation/ComplianceEvaluationNotificationSlip/HgpjtzMain.vue');
export const RiskWarningChartIndex = () => import('../Compliance/chart/RiskWarningChartIndex.vue');


export const HgtjfxIndex = () => import('../Compliance/Management/ComplianceOperation/StatisticalAnalysis/HgtjfxIndex.vue');

export const ZcfgkTz = () => import('../Compliance/CulturalManagement/ComplianceRegulationsLibrary/ZcfgkTz.vue');

export const HgalkTz = () => import('../Compliance/CulturalManagement/ComplianceCaseLibrary/HgalkTz.vue');

export const HgscTz = () => import('../Compliance/Management/ComplianceOperation/ComplianceReport/HgscTz.vue');

export const ContractReport = () => import('../report/ContractReport.vue');
