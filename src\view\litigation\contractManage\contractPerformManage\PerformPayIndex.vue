<template>
  <el-container class="container-manage-sg" direction="vertical">
    <el-header>
      <el-card>
        <div>
          <el-input
              v-model="selectData.fuzzyValue"
              class="filter_input"
              clearable
              placeholder="检索字段（合同名称、合同编码、合同类型、合同金额、对方签约主体）"
              @clear="search_"
              @keyup.enter.native="search_"
          >
            <el-popover slot="prepend" placement="bottom-start" trigger="click" width="1000">
              <el-form ref="queryForm" label-width="100px" size="mini">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="合同编码">
                      <el-input v-model="selectData.contractCode" clearable placeholder="请输入..."/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="合同名称">
                      <el-input v-model="selectData.contractName" clearable placeholder="请输入..."/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="合同类型">
                      <el-input v-model="selectData.contractType" clearable placeholder="请输入..."/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="经办时间">
                      <el-date-picker v-model="selectData.startTimeMin" placeholder="选择日期"
                                      style="width: 45%; float: left"
                                      type="date"/>
                      <div class="label_1" style="width: 10%; float: left; text-align: center">
                        <span>至</span>
                      </div>
                      <el-date-picker v-model="selectData.startTimeMax" placeholder="选择日期" style="width: 45%"
                                      type="date"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="对方签约主体">
                      <el-input v-model="selectData.otherPartyName" clearable placeholder="请输入..."/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="我方签约主体">
                      <el-input v-model="selectData.ourPartyName" clearable placeholder="请输入..."/>
                    </el-form-item>
                  </el-col>

                </el-row>
                <el-button-group style="float: right">
                  <el-button icon="el-icon-search" size="mini" type="primary" @click="search_">搜索</el-button>
                  <el-button icon="el-icon-refresh-left" size="mini" type="primary" @click="empty_">重置</el-button>
                </el-button-group>
              </el-form>
              <el-button slot="reference" size="small" type="primary">高级检索</el-button>
            </el-popover>
            <el-button slot="append" icon="el-icon-search" @click="search_"/>
          </el-input>
        </div>
      </el-card>
    </el-header>
    <el-main>
      <SimpleBoardIndex :title="'履行计划跟踪（支出）'">
        <template slot="button">
          <el-button class="normal-btn" size="mini" type="primary" @click="exportAll">导出全部</el-button>
          <el-button class="normal-btn" size="mini" type="primary" @click="exportCurrent">导出当前</el-button>
        </template>
        <el-table id="contractList" ref="table" :data="tableData" :height="table_height" :show-overflow-tooltip="true"
                  :span-method="objectSpanMethod" border fit highlight-current-row
                  stripe
                  style="width: 100%"
        >

          <el-table-column align="center" label="序号" type="index" width="50"/>
          <el-table-column label="合同编码" min-width="200" prop="contractCode" show-overflow-tooltip/>
          <el-table-column label="合同名称" min-width="350" prop="contractName" show-overflow-tooltip/>
          <el-table-column label="合同性质" min-width="100" prop="dataTypeName" show-overflow-tooltip/>
          <el-table-column label="合同类型" min-width="150" prop="contractType" show-overflow-tooltip/>
          <el-table-column label="合同金额" prop="contractMoney" show-overflow-tooltip width="100"/>
          <el-table-column label="变更后金额" min-width="180" prop="afterChangeMoney"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="已履行金额" min-width="180" prop="fulfillmentAmount"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="待履行金额" min-width="180" prop="contractExecutoryMoney"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="履行进度" min-width="100">
            <template slot-scope="scope">
              <el-progress :percentage="scope.row.performProgress==null?0:scope.row.performProgress"
                           :status="scope.row.performProgress===100?'success':'warning'"
                           :stroke-width="26"
                           :text-inside="true"></el-progress>
            </template>
          </el-table-column>
          <el-table-column label="履行计划编号" min-width="220" prop="performCode" show-overflow-tooltip/>
          <el-table-column align="center" label="履行事项" prop="performText" show-overflow-tooltip width="200"/>
          <el-table-column align="center" label="我方签约主体" prop="ourPartyName" show-overflow-tooltip width="230"/>
          <el-table-column align="center" label="对方签约主体" min-width="250" prop="otherPartyName"
                           show-overflow-tooltip/>
          <el-table-column align="center" label="合同条款" prop="contractTerms" show-overflow-tooltip width="150"/>
          <el-table-column align="center" label="结算方式" prop="settlementMethod" show-overflow-tooltip width="230"/>
          <el-table-column align="center" label="计划履行日期" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.planDate | parseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="计划履行金额" prop="planAmount"
                           width="100"></el-table-column>
          <el-table-column align="center" label="计划履行比例" width="150">
            <template slot-scope="scope">
              <span>{{ scope.row.planRatio }}%</span>
            </template>
          </el-table-column>
          <!--<el-table-column label="流程状态" min-width="100" prop="dataState" show-overflow-tooltip />
          <el-table-column label="变更状态" min-width="100" prop="changeState" show-overflow-tooltip />
          <el-table-column label="履行状态" min-width="100" prop="performState" show-overflow-tooltip
                           />
          <el-table-column label="关闭状态" min-width="100" prop="closeState" show-overflow-tooltip />-->
        </el-table>
      </SimpleBoardIndex>
    </el-main>
    <el-footer>
      <pagination
          :limit.sync="selectData.limit"
          :page.sync="selectData.page"
          :total="selectData.total"
          @pagination="refreshData"
      />
    </el-footer>

  </el-container>
</template>

<script>
import {mapGetters} from "vuex"
import bmContractPerformApi from '@/api/contract/bmContractPerform'
import pagination from '../../../components/Pagination/PaginationIndex'
import SimpleBoardIndex from "@/view/components/SimpleBoard/SimpleBoardIndex"

export default {
  name: 'PerformPayIndex',
  inject: ['layout'],
  components: {pagination, SimpleBoardIndex},
  computed: {
    ...mapGetters(["orgContext"]),
  },
  data() {
    return {
      selectData: {
        contractCode: null,
        performCode: null,
        contractName: null,
        contractType: null,
        fuzzyValue: null,
        page: 1,
        limit: 10,
        total: 0,
        sortName: null,
        order: false,
        orgId: null,
      },
      table_height: '100%',
      tableData: [],
      spanMap: new Map(), // 用于存储每一列的合并信息

    }
  },
  created() {
    this.refreshData()
    window.vm = this;
  },
  activated() {
    this.refreshData()
  },
  deactivated() {

  },
  mounted() {
    this.$nextTick(function () {
      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 45
      // 监听窗口大小变化
      const self = this
      window.onresize = function () {
        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 45
        self.otherHeight = self.table_height
      }
      this.otherHeight = this.table_height
    })
  },
  methods: {
    // 刷新或查询数据
    refreshData() {
      this.selectData.orgId = this.orgContext.currentOrgId
      this.selectData.payTypeCode = '0';
      bmContractPerformApi.loadPayPerfomList(this.selectData).then(response => {
        this.tableData = response.data.data.records
        this.selectData.total = response.data.data.total
        this.typeName = null
        // 为每一列计算合并信息
        // 计算 name 列的合并信息
        this.spanMap = this.calculateSpans(this.tableData, 'contractCode');
      })
    },
    // 搜索
    search_: function () {
      this.selectData.page = 1
      this.refreshData()
    },
    // 清空
    empty_() {
      this.selectData = {
        contractCode: null,
        performCode: null,
        contractName: null,
        contractType: null,
        fuzzyValue: null,
        page: 1,
        limit: 10,
        total: 0,
        sortName: null,
        order: false,
        orgId: null,
      }
      this.refreshData()
    },
    // 刷新
    refresh_() {
      this.selectData.contractName = ''// 合同名称
      this.selectData.contractTypeName = ''// 合同类型
      this.selectData.fuzzySearch = ''// 模糊搜索
      this.selectData.sortName = null
      this.selectData.order = false
      this.refreshData()
    },

    tableSort(column, prop, order) {
      this.selectData.sortName = column.prop
      this.selectData.order = column.order === "ascending"
      this.refreshData()
    },


    // 计算 name 列的合并信息
    calculateSpans(data, key) {
      const spanMap = new Map();
      let prevValue = null;
      let startIndex = 0;

      // 遍历数据，计算合并信息
      data.forEach((row, index) => {
        const currentValue = row[key];
        if (currentValue !== prevValue) {
          // 如果当前值与前一个值不同，记录合并信息
          if (prevValue !== null) {
            spanMap.set(startIndex, index - startIndex);
          }
          startIndex = index;
          prevValue = currentValue;
        }
      });

      // 处理最后一组
      if (prevValue !== null) {
        spanMap.set(startIndex, data.length - startIndex);
      }

      return spanMap;
    },

    // 合并行的方法
    objectSpanMethod({row, column, rowIndex, columnIndex}) {
      // 只对 name、age、address 列应用合并
      if (['contractCode', 'contractName', 'contractType', 'dataTypeName', 'contractMoney', 'afterChangeMoney', 'fulfillmentAmount', 'contractExecutoryMoney'].includes(column.property)) {
        for (const [startRow, span] of this.spanMap.entries()) {
          if (rowIndex >= startRow && rowIndex < startRow + span) {
            if (rowIndex === startRow) {
              // 如果是合并的起始行，返回合并的行数
              return {rowspan: span, colspan: 1};
            } else {
              // 如果不是起始行，返回不合并
              return {rowspan: 0, colspan: 0};
            }
          }
        }
      }
      // 默认不合并
      return {rowspan: 1, colspan: 1};
    },
    getProgress(row) {
      //                debugger;
      let totalAmount = row.contractMoney;
      if (row.afterChangeMoney !== '' && row.afterChangeMoney != null) {
        totalAmount = row.afterChangeMoney;
      }
      if (row.fulfillmentAmount == null || row.fulfillmentAmount === '') {
        return 0;
      } else {
        let num = row.fulfillmentAmount / totalAmount * 100
        return num.toFixed(0);
      }
    },
    exportAll() {
      let obj = {orgId: this.orgContext.currentOrgId};
      bmContractPerformApi.exportContractPerformDataList(obj).then(res => {
        console.log('下载的文件', res)
        const link = document.createElement('a');
        try {
          let blob = res.data
          console.log(blob);
          // let _fileName = res.headers['content-disposition'].split(';')[1].split('=')[1];//文件名，中文无法解析的时候会显示 _(下划线),生产环境获取不到
          // 从content-disposition拿到后端返回的filename，并用decodeURI解码，防止乱码
          let _fileName = decodeURI((res.headers['content-disposition'].split(';')[1].split("="))[1])
          link.style.display = 'none';
          // 兼容不同浏览器的URL对象
          const url = window.URL || window.webkitURL || window.moxURL;
          link.href = url.createObjectURL(blob);
          link.download = _fileName;
          link.click();
          window.URL.revokeObjectURL(url);
        } catch (e) {
          console.log('下载的文件出错', e)
        }
      })
    },
    exportCurrent() {
      this.selectData.orgId = this.orgContext.currentOrgId
      bmContractPerformApi.exportContractPerformDataList(this.selectData).then(res => {
        console.log('下载的文件', res)
        const link = document.createElement('a');
        try {
          let blob = res.data
          console.log(blob);
          // let _fileName = res.headers['content-disposition'].split(';')[1].split('=')[1];//文件名，中文无法解析的时候会显示 _(下划线),生产环境获取不到
          // 从content-disposition拿到后端返回的filename，并用decodeURI解码，防止乱码
          let _fileName = decodeURI((res.headers['content-disposition'].split(';')[1].split("="))[1])
          link.style.display = 'none';
          // 兼容不同浏览器的URL对象
          const url = window.URL || window.webkitURL || window.moxURL;
          link.href = url.createObjectURL(blob);
          link.download = _fileName;
          link.click();
          window.URL.revokeObjectURL(url);
        } catch (e) {
          console.log('下载的文件出错', e)
        }
      })
    }


  }
}
</script>

<style scoped>
.el-table__fixed-body-wrapper {
  top: 50px !important;
}

.filter_input .el-input-group__prepend {
  color: #FFFFFF;
  background-color: #1890ff;
  border-color: #1890ff;
}

.label_1 {
  text-align: right;
  padding-right: 5px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}

.myNew .el-input__inner {
  background: #1F95FF;
}

.myNew .el-input__suffix {
  color: white;
}

.myNew input::-webkit-input-placeholder {
  color: white;
}

.mylabel .el-form-item--mini .el-form-item__label {
  line-height: 15px;
}

.scrollClass {
  /* margin-top: 10px; */
  height: 50vh;
}

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden !important;
}

/deep/ .el-scrollbar__wrap {
  overflow-x: hidden !important;
}

/* 树形结构添加横向滚动条 */
::v-deep .el-tree > .el-tree-node {
  width: 120%;
  display: inline-block;
}

/deep/ .el-tree > .el-tree-node {
  width: 100%;
  height: 100%;
  overflow: auto;
}
</style>
