<template>
  <FormWindow v-if="dialogVisible" ref="formWindow" obj-id="main">
    <div class="contract-perform-edit-dialog">
      <el-dialog :close-on-click-modal="false" :visible.sync="dialogVisible" style="font-size:28px" title="合同履行计划"
                 width="85%"
                 @close="closeDialog">
        <div style="height: calc(80vh - 60px - 55px - 40px);overflow: auto;">
          <el-form ref="dataForm" :model="dataTemp" :rules="rules" label-position="right" label-width="100px"
                   style="width: 100%;   padding: 20px;">
            <el-row>
              <!--<el-col :span="12">
                  <el-form-item label="合同编号" prop="contractCode">
                      <el-input v-model="dataTemp.contractCode" style="width:100%" readonly></el-input>
                      &lt;!&ndash;                                <span  class="viewSpan">{{ dataTemp.contractCode }}</span>&ndash;&gt;
                  </el-form-item>
              </el-col>-->
              <el-col :span="24">
                <el-form-item label="履行事项" prop="performText">
                  <el-input v-model="dataTemp.performText" placeholder="请输入履行事项" style="width:100%"></el-input>
                  <!--                                <span  class="viewSpan">{{ dataTemp.performText }}</span>-->
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="合同条款" prop="contractTerms">
                  <el-input v-model="dataTemp.contractTerms" placeholder="请输入合同条款"
                            show-word-limit style="width:100%"/>
                  <!--                                <text-span  class="viewSpan" :text=" dataTemp.contractTerms"/>-->
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="生效文件">
                  <uploadDoc
                      v-model="dataTemp.contractTakeEffectFile"
                      :disabled="true"
                      :doc-path="'/contract'"
                      :files.sync="dataTemp.contractTakeEffectFile"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="我方签约主体" prop="ourPartyName">
                  <!-- <el-input v-model="dataTemp.ourPartyName"
                             placeholder="请输入内容" show-word-limit/>-->
                  <el-select v-model="ourPartyName" style="width:100%">
                    <el-option v-for="item in ourPartyNameArr" :label="item" :value="item"></el-option>
                  </el-select>
                  <!--                                <text-span  class="viewSpan" :text=" dataTemp.ourPartyName"/>-->
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="对方签约主体" prop="otherPartyName">
                  <!-- <el-input v-model="dataTemp.otherPartyName"
                             placeholder="请输入内容" show-word-limit/>-->
                  <el-select v-model="otherPartyName" style="width:100%">
                    <el-option v-for="(item,i) in otherPartyNameArr" :key="i" :label="item" :value="item"></el-option>
                  </el-select>
                  <!--                                <text-span  class="viewSpan" :text=" dataTemp.otherPartyName"/>-->
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>

              <el-col :span="6">
                <el-form-item label="收支方向" prop="revenueExpenditure">
                  <el-input v-model="dataTemp.revenueExpenditure"
                            disabled placeholder="请输入内容" show-word-limit/>
                  <!--                                <text-span  class="viewSpan" :text=" dataTemp.revenueExpenditure"/>-->
                </el-form-item>

              </el-col>
              <el-col :span="6">
                <el-form-item label="结算方式" prop="settlementMethod">
                  <el-input v-model="dataTemp.settlementMethod"
                            disabled placeholder="请输入内容"/>
                  <!--                                <text-span  class="viewSpan" :text=" dataTemp.settlementMethod"/>-->
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="计划履行日期" prop="planDate">
                  <el-date-picker v-model="dataTemp.planDate"
                                  style="width: 100%;" type="date" value-format="yyyy-MM-dd"/>
                  <!--                                <span  class="viewSpan">{{ dataTemp.planDate | parseTime('{y}-{m}-{d}') }}</span>-->
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="计划履行金额" prop="planAmount">
                  <el-input-number v-model="dataTemp.planAmount" :controls="false"
                                   :placeholder="'请输入内容，可分配项目额：'+this.maxMoney"
                                   show-word-limit style="width:100%"
                                   @change="caluateRmb"/>
                  <!--                                <text-span  class="viewSpan" :text=" dataTemp.planAmount"/>-->
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="6">
                <el-form-item v-if="projectList.length>0"
                              :rules="{ required: true, message: '请选择项目编号', trigger: 'blur' }"
                              label="项目编号"
                              prop="projectCodeArr">
                  <el-select v-model="dataTemp.projectCodeArr" filterable multiple
                             placeholder="请选择" style="width: 100%;">
                    <el-option v-for="(item,i) in projectList" :key="item.id"
                               :label="item.projectCode" :value="item.projectCode">
                    </el-option>
                  </el-select>
                  <!--                                <text-span  class="viewSpan" :text=" dataTemp.projectCode"/>-->
                </el-form-item>
                <el-form-item v-if="projectList.length==0" label="项目编号" prop="projectCodeArr">
                  <el-select v-model="dataTemp.projectCodeArr" filterable multiple
                             placeholder="请选择" style="width: 100%;">
                    <el-option v-for="(item,i) in projectList" :key="item.id"
                               :label="item.projectCode" :value="item.projectCode">
                    </el-option>
                  </el-select>
                </el-form-item>

              </el-col>
              <el-col :span="6">
                <el-form-item label="币种" prop="currency">
                  <el-input v-model="dataTemp.currency" disabled readonly
                            style="width: 100%;"/>
                  <!--                                <span  class="viewSpan">{{ dataTemp.currency}}</span>-->
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="汇率方式" prop="exchangeRateMethod">
                  <el-input v-model="dataTemp.exchangeRateMethod" disabled readonly>
                  </el-input>
                  <!--                                <text-span  class="viewSpan" :text=" dataTemp.exchangeRateMethod"/>-->
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="汇率" prop="exchangeRate">
                  <el-input v-model="dataTemp.exchangeRate" disabled readonly
                            style="width: 100%;"/>
                  <!--                                <span  class="viewSpan">{{ dataTemp.exchangeRate}}</span>-->
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item class="custom-word-break" label="计划履行金额（人民币）" prop="planAmountRmb">
                  <el-input-number v-model="dataTemp.planAmountRmb" :controls="false" disabled
                                   style="width:100%"></el-input-number>
                  <!--                                <text-span  class="viewSpan" :text=" dataTemp.exchangeRateMethod"/>-->
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="账期天数" prop="periodDay">
                  <span slot="label">账期天数
                    <el-tooltip effect="light">
                      <i class="el-icon-info"/>
                      <div slot="content">
                        指合同条款中约定的从开票日到付款日之间的时间间隔。<br>若合同条款中未明确约定账期天数，请根据本单位或上级管理单位账期管理规定填写。
                      </div>
                    </el-tooltip>
                  </span>
                  <el-input
                      v-model.number="dataTemp.periodDay"
                      clearable
                      placeholder="请输入整数"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="计划开票时间" prop="planInvoiceDate">
                  <el-date-picker v-model="dataTemp.planInvoiceDate"
                                  style="width: 100%;"/>
                  <!--                                <span  class="viewSpan">{{ dataTemp.planInvoiceDate}}</span>-->
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="履行说明" prop="performExplain">
                  <el-input
                      v-model="dataTemp.performExplain"
                      :autosize="{ minRows: 3, maxRows: 6 }"
                      maxlength="1000"
                      placeholder="请输入内容"
                      show-word-limit
                      type="textarea"/>
<!--                  <textarea v-model="dataTemp.performExplain" cols="2" style="width:100%"></textarea>-->
                  <!--                                <text-span  class="viewSpan" :text="dataTemp.performExplain"/>-->
                </el-form-item>
              </el-col>
            </el-row>


          </el-form>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="saveData">确定</el-button>
          <el-button @click="closeDialog">取消</el-button>
        </div>

      </el-dialog>
    </div>

  </FormWindow>
</template>
<script>
import bmContractApi from '@/api/contract/bmContract';
import bmContractPerform from '@/api/contract/bmContractPerform';
import FormWindow from '@/view/components/FormWindow/FormWindow'
// vuex缓存数据
import {mapGetters} from 'vuex'
import UploadDoc from "@/view/components/UploadDoc/UploadDoc.vue";

export default {
  name: 'contractPerformEditDialog',
  components: {
    UploadDoc,
    FormWindow
  },
  computed: {
    ...mapGetters(['orgContext'])
  },
  data() {
    return {
      title: '',
      dataTemp: {},
      otherPartyName: '',
      ourPartyName: '',
      projectList: [],
      dialogVisible: false,
      rules: {
        performText: [{required: true, message: '请输入履行事项', trigger: 'blur'}],
        contractTerms: [{required: true, message: '请输入合同条款', trigger: 'blur'}],
        planDate: [{required: true, message: '请输入计划履行日期', trigger: 'blur'}],
        planAmount: [{required: true, message: '请输入计划履行金额', trigger: 'blur'}],
        // periodDay: [{required: true, message: '请输入账期天数', trigger: 'blur'}],
        periodDay: [
          {required: true, message: '请输入账期天数'}, // 必填验证
          {
            validator: (rule, value, callback) => {
              if (value === '' || value === null) {
                callback(new Error('请输入数值'));
              } else if (!Number.isInteger(value)) {
                callback(new Error('必须输入整数'));
              } else {
                callback();
              }
            }, trigger: 'blur'
          } // 整数验证
        ]
//                    projectCodeArr:[{ required: true, message: '请选择项目编号', trigger: 'blur' }],
      },
      ourPartyNameArr: [],
      otherPartyNameArr: [],
      maxMoney: 0,

    }
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: () => {
      }
    }
  },
  watch: {
    show() {
      this.dialogVisible = this.show;
      window.vm = this;
    },
    rowData() {
      if (this.show) {
        this.ourPartyNameArr = this.rowData.ourPartyName.split(",");
        this.otherPartyNameArr = this.rowData.otherPartyName.split(",");
        if (this.rowData.type == 'add') {
          //this.dataTemp = this.rowData;
          Object.assign(this.dataTemp, this.rowData);
          this.loaddingProjectList(this.rowData.id);
          this.dataTemp.contractId = this.rowData.id;
          this.dataTemp.currency = this.rowData.currency;
          if (this.rowData.ourPartyName.indexOf(",") == -1) {
            this.dataTemp.ourPartyName = this.rowData.ourPartyName;
            this.ourPartyName = this.rowData.ourPartyName;
          } else {
            this.dataTemp.ourPartyName = "";
          }
          if (this.dataTemp.otherPartyName.indexOf(",") == -1) {
            this.dataTemp.otherPartyName = this.rowData.otherPartyName;
            this.otherPartyName = this.rowData.otherPartyName;
          } else {
            this.dataTemp.otherPartyName = '';
          }
          this.addOrgData();
          this.maxMoney = this.rowData.elsePlanAmount;
        } else if (this.rowData.type == 'edit') {
          //Object.assign(this.dataTemp,this.rowData);
          this.dataTemp = this.rowData;
          this.otherPartyName = this.dataTemp.otherPartyName;
          this.ourPartyName = this.dataTemp.ourPartyName;
//                        console.log(3333);
//                        console.log(this.dataTemp.projectCode);
//                        if (this.dataTemp.projectCode.indexOf(",")!=-1){
//                        }
          this.dataTemp.projectCodeArr = this.dataTemp.projectCode.split(",");
          this.loaddingProjectList(this.rowData.contractId);
          this.maxMoney = this.rowData.elsePlanAmount;
        } else if (this.rowData.type == 'view') {
          this.dataTemp = this.rowData;
        } else if (this.rowData.type == 'copy') {
//                        Object.assign(this.dataTemp,this.rowData);
          this.dataTemp = this.rowData;
          this.otherPartyName = this.dataTemp.otherPartyName;
          this.ourPartyName = this.dataTemp.ourPartyName;
          this.dataTemp.serialNumber = null;
//                        this.dataTemp.projectCodeArr = this.dataTemp.projectCode.split(",");
          if (this.dataTemp.projectCode.indexOf(",") != -1) {
            this.dataTemp.projectCodeArr = this.dataTemp.projectCode.split(",");
          }
          this.loaddingProjectList(this.rowData.contractId);
          this.dataTemp.id = null;
          this.maxMoney = this.rowData.elsePlanAmount;
          this.dataTemp.planAmount = undefined;
          this.addOrgData();
        }
      }
    }
  },
  methods: {
    caluateRmb() {
      let money = this.dataTemp.planAmount * this.dataTemp.exchangeRate;
      this.dataTemp.planAmountRmb = money.toFixed(2);
    },
    addOrgData() {
      this.dataTemp.createOgnId = this.orgContext.currentOgnId
      this.dataTemp.createOgnName = this.orgContext.currentOgnName
      this.dataTemp.createDeptId = this.orgContext.currentDeptId
      this.dataTemp.createDeptName = this.orgContext.currentDeptName
      this.dataTemp.createPsnId = this.orgContext.currentPsnId
      this.dataTemp.createPsnName = this.orgContext.currentPsnName
      this.dataTemp.createPsnFullId = this.orgContext.currentPsnFullId
      this.dataTemp.createPsnFullName = this.orgContext.currentPsnFullName
      this.dataTemp.createPsnPhone = this.orgContext.currentPsnPhone
      this.dataTemp.createGroupId = this.orgContext.currentGroupId
      this.dataTemp.createGroupName = this.orgContext.currentGroupName
      this.dataTemp.createOrgId = this.orgContext.currentOrgId
      this.dataTemp.createOrgName = this.orgContext.currentOrgName
      this.dataTemp.createLegalUnitId = this.orgContext.currentLegalUnitId
      this.dataTemp.createLegalUnitName = this.orgContext.currentLegalUnitName
      this.dataTemp.createTime = new Date()
    },
    saveData() {
      let type = this.rowData.type;
      this.$refs['dataForm'].validate((valid) => {
        if (this.dataTemp.planAmount > this.maxMoney) {
          this.$message.error("输入的已履行金额，已超过最大可分配金额，请调整！");
        } else {
          if (valid) {
            this.dataTemp.ourPartyName = this.ourPartyName;
            this.dataTemp.otherPartyName = this.otherPartyName;
            if (type != 'edit') {
              delete this.dataTemp.type;
              delete this.dataTemp.elsePlanAmount;
              this.dataTemp.projectCode = this.dataTemp.projectCodeArr.join(",");
              this.dataTemp.projectCodeArr = null;
              bmContractPerform.addContractPerform(this.dataTemp).then(res => {
                console.log(res.status);
                if (res.status == 200) {
                  this.$message.success('添加成功');
                  this.closeDialog();
                } else {
                  this.$message.error(res.data.msg);
                }
              })
            } else {
              delete this.dataTemp.type;
              delete this.dataTemp.projectCodeArr;
              delete this.dataTemp.elsePlanAmount;
              bmContractPerform.updateContractPerform(this.dataTemp).then(res => {
                if (res.status == 200) {
                  this.$message.success('更新成功');
                  this.closeDialog();
                } else {
                  this.$message.error(res.data.msg);
                }
              })
            }
          }
        }
      })
    },
    closeDialog() {
      this.projectList = [];
      this.dialogVisible = false;
      this.ourPartyNameArr = [];
      this.otherPartyNameArr = [];
      this.$emit('listenToChildEvent', false, this.dataTemp.contractId);
      this.dataTemp = {};
    },
    loaddingProjectList(contractId) {
      bmContractApi.queryById({id: contractId}).then(res => {
        if (res.data.data.projectList != null) {
          this.projectList = res.data.data.projectList;
        }
      });
    }

  }
}
</script>