<template>
  <keep-alive>
    <component :is="componentName" v-if="tabName === $route.params.component" :key="$route.params.component"
      @custom="custom">
      <!--                 select-user-component-name="OrgTree"-->
      <!--                 tree-select-user-component-name="OrgTree"-->
    </component>
  </keep-alive>
</template>

<script>
import {
  Announcement,
  AnnouncementView,
  AuthorizationApprovalIndex,
  AutSealTakeEffectMain,
  BidManagementIndex,
  BidManagementMain,
  BlackIndex,
  BlackLedger,
  BlackMain,
  BusinessTypeIndex,
  CompanyContractIndex,
  condition,
  conditionMain,
  ContractApprovalIndex,
  ContractApprovalMain,
  ContractApprovalMainDetail,
  ContractApprovalSuppleIndex,
  ContractAppSuppleMainDetail,
  ContractChangeMain,
  ContractChangeMainDetail,
  ContractChangeMainSuppleDetail,
  ContractChartIndex,
  contractCloseIndex,
  ContractCloseMain,
  ContractCloseMainDetail,
  ContractEp,
  ContractEvaluateDetailMain,
  ContractEvaluateIndex,
  ContractEvaluateMain,
  ContractEvaluateTaskMain,
  ContractExcIndex,
  ContractExcBook,
  ContractExcDetail,
  ContractFileIndex,
  ContractFileMain,
  ContractIndex,
  ContractLedgerIndex,
  RiskIndex,
  ContractMoreMainDetail,
  ContractOppositeIndex,
  ContractOppositeMain,
  ContractOurIndex,
  ContractProjectIndex,
  ContractProjectMain,
  ContractSecureMain,
  ContractSecureMainDetail,
  ContractStatistics,
  ContractStopMain,
  ContractStopMainDetail,
  ContractSupplementChange,
  ContractSupplementMain,
  ContractTemplateIndex,
  ContractTemplateMain,
  ContractTypeIndex,
  GeneralContractIndex,
  InternalProjectIndex,
  InternalProjectMain,
  knlIndex,
  PerformIndex,
  PerformPayIndex,
  PerformReceivePayIndex,
  PerformResultClaimIndex,
  PerformResultIndex,
  SealApprovalAppIndex,
  SealApprovalIndex,
  SealApprovalMain,
  SealApprovalMainDetail,
  SealApprovalSuppleAppIndex,
  SealIndex,
  SealLedger,
  SealMain,
  SealStampMain,
  SealTakeEffectMain,
  StampDutyIndex,
  StampDutyMain,
  StandardText,
  SuppSealApprovalMain,
  SuppSealApprovalMainDetail,
  TemplateUsageLedger,
  updatecontractapi,
  updateContractMain,
  UserManual,
  UserCustomMenu

} from './view/module/contract';

import {
  AlphaCaseDetail,
  CaseExamineIndex,
  CaseExamineMain,
  CaseExamineMainDetail,
  CaseIndex,
  CaseInterfileMain,
  CaseLevelIndex,
  CaseLevelLedger,
  CaseLevelMain,
  CaseLevelMainDetail,
  CaseLook,
  CaseMain,
  CaseMainView,
  CaseProsecutionIndex,
  CaseProsecutionLedger,
  CaseProsecutionMain,
  CaseProsecutionMainDetail,
  CaseReportAnnualStatistics,
  CaseReportIndex,
  caseReportLedger,
  CaseReportMain,
  CaseReportQuarterStatistics,
  CaseStatistical,
  CaseSuedIndex,
  CaseSuedMain,
  CaseSuedMainDetail,
  CaseSuperviseIndex,
  CaseSuperviseLedger,
  CaseSuperviseMain,
  CaseSuperviseMainDetail,
  ClassCaseQuery,
  DictIndex,
  GenerateReport,
  ProcessIndex,
  CaseRiskIndex,
  CaseRisksMain,
  CaseRiskMainDetail,
  CaseRiskLedger,
} from './view/module/case';
import { CaseProjectIndex, CaseProjectMain, CaseProjectView } from './view/module/caseProject';
import { CaseAbstract, CaseLedgerIndex, CaseLedgerLook, CaseLedgerView } from './view/module/caseLedger';
import {
  CooperationIndex,
  CooperationMain,
  CooperationView,
  TransferIndex,
  TransferMain,
  TransferView,
  TurnoverIndex,
  TurnoverMain,
  TurnoverView,
} from './view/module/cooperation';
import { CaseBaseIndex, CaseListloe, CaseListpet, PreservationLedge } from './view/module/caseBase';
import { CaseAnalysisIndex, Frame } from './view/module/caseAnalysis';
import { commentsIndex, message, page } from './view/module/page';
import { RemindingIndex, RemindingMain, TaskIndex } from './view/module/reminding';
import {
  CaseRiskMain,
  ComplianceRisksIndex,
  ComplianceRisksMainDetail,
  DisputeApprovalIndex,
  DisputeApprovalLedger,
  DisputeApprovalMain,
  DisputeApprovalMainDetail,
  DisputeIndex,
  DisputeMain,
  DisputeProcessLedger,
} from './view/module/dispute';
import { AdminConfigIndex, UserConfigIndex, OgnConfigIndex,UserPartIndex,ProcessUserIndex } from './view/module/basic';
import {
  AutDetail,
  AutEvaluationLedger,
  AuthorizationContractMain,
  AuthorizationContractMainDetail,
  AuthorizationIndex,
  AuthorizationLedger,
  AuthorizationMain,
  AuthorizationMainDetail,
  AuthorizationMatterIndex,
  AuthorizationMoreMainDetail,
  ChangeMain,
  ChangeMainDetail,
  LitigationMain,
  LitigationMainDetail,
  RoutineMain,
  RoutineMainDetail,
  ShareholderMain,
  ShareholderMainDetail,
  SublicenseMain,
  SublicenseMainDetail,
  TerminationMain,
  TerminationMainDetail,
} from './view/module/authorization';
import { CenterIndex } from './view/module/center';
import {
  FirmBlackIndex,
  FirmBlackLedger,
  FirmBlackMain,
  FirmBlackMainDetail,
  FirmEvaluateIndex,
  FirmEvaluateMain,
  FirmEvaluateTaskMain,
  FirmIndex,
  FirmInLedger,
  FirmLedgerIndex,
  FirmLedgerLawFirm,
  FirmLedgerLawyer,
  FirmMainChange,
  FirmMainChangeDetail,
  FirmMainNew,
  FirmMainNewDetail,
  FirmMainRecommend,
  FirmMainRecommendDetail,
  FirmOutIndex,
  FirmOutLedger,
  FirmOutMain,
  FirmOutMainDetail,
  FirmSelectCompareSupplement,
  FirmSelectDirectSupplement,
  FirmSelectIndex,
  FirmSelectLedgerIndex,
  FirmSelectMainCompare,
  FirmSelectMainCompareDetail,
  FirmSelectMainDirect,
  FirmSelectMainDirectDetail,
  InternalCounselorIndex,
  InternalCounselorMain,
  LawFirmDialog,
  LawyerBlackIndex,
  LawyerBlackListMain,
  LawyerBlackMainDetail,
  LawyerDialog,
  LawyerIndex,
  LawyerMainNewDetail,
  LawyerOutIndex,
  LawyerOutMain,
  LawyerOutMainDetail,
  LawyerUseIndex,
  LawyerUseMainDetail,
  MainLawyer,
} from './view/module/lawFirm';

import processApi from '@/api/_system/process';
import { SpecialIndex, SpecialMain } from '@/view/module/special';
import {
  AuditTrailDetail,
  AuditTrailIndex,
  CheckResultDetail,
  CheckResultIndex,
  CheckResultMain,
  ComplianceRiskWarningDetial,
  CompolianceRiskWarning,
  CompolianceRiskWarningMain,
  FxgcspIndex,
  FxgcspMain,
  FxgcspMainDetail,
  GwzzqdIndex,
  GwzzqdLedgerIndex,
  GwzzqdMainDetail,
  HgalkIndex,
  HgalkMainDetail,
  HgbgIndex,
  HgbgMain,
  HgbgMainDetail,
  HgfxsbIndex,
  HgfxsbLedgerIndex,
  hgfxsbMainDetail,
  HggfglIndex,
  HggfglMainDetail,
  HgpjIndex,
  HgpjtzIndex,
  HgpjtzMain,
  HgpjtzMainDetail,
  HgpjMainDetail,
  HgpxxxglDetail,
  HgpxxxglIndex,
  HgpxxxglMainDetail,
  HgpxxxyhDetail,
  HgpxxxyhIndex,
  HgryDetail,
  HgrykIndex,
  HgrykIndexRegister,
  HgscIndex,
  CjwtallIndex,
  HgscMain,
  HgscMainDetail,
  HgscMainHfhg,
  HgscMainDetailHfhg,
  HgscMainQsss,
  HgscMainDetailQsss,
  HgscMainTsjy,
  HgscMainDetailTsjy,
  HgscMainZdhf,
  HgscMainDetailZdhf,
  HgwhxcIndex,
  CjwttzIndex,
  CjwttzMainDetail,
  HgwhxcMainDetail,
  HgwzIndex,
  HgwzMain,
  XtjdMain,
  HgwzMainDetail,
  HgyxxpjMain,
  HgzxjhMain,
  ProjectDemonstrationMain,
  HgzzjsIndex,
  LcgkqdIndex,
  LcgkqdLedgerIndex,
  LcgkqdMainDetail,
  ProjectDemonstrationIndex,
  ProjectDemonstrationDetail,
  PublishCheckDetail,
  PublishCheckIndex,
  RectificationPlanDetail,
  RectificationPlanIndex,
  ResultFeedbackDetail,
  ResultsFeedbackIndex,
  RiskWaringIndex,
  RiskWaringMain,
  RiskWaringMainApproval,
  RiskWarningDataBase,
  RiskdataLedgerIndex,
  RiskWarningDistribution,
  RiskWarningReceive,
  RiskWarningStandingBook,
  RiskWaringEventBook,
  SelectedCheckIndex,
  TzxmfxIndex,
  TzxmfxMain,
  TzxmfxMainDetail,
  WgjbIndex,
  WgjbgsIndex,
  ZcfgkIndex,
  ZcfgkMainDetail,
  RiskApprovalProcessIndex,
  RiskApprovalProcessMainDetail,
  RiskApprovalProcessMain,
  RiskWarningChartIndex,
  HgtjfxIndex,
  HgscTz,
  HgalkTz,
  ZcfgkTz,
  ContractReport
} from '@/view/module/complianceRisk';

import {
  ProbjectMajorReasoningIndex,
  ProbjectMajorReasoningMain,
  ProjectMajorReasoningApp,
  ProbjectMajorReasoningBook,
  ProjectMajorSummaryIndex,
  ProjectMajorSummaryMain,
  ProjectMajorSummaryApp,
  ProjectDistribution,
  ProjectDistributionDetail,
  ResultFeedBack,
  ProbjectMajorReasoningBookDetail
} from '@/view/module/probjectReasoning';

import { FrbbIndex,Frbb2,Frbb3,Frbb4,Frbb5,Frbb6 } from '@/view/module/fanruan';

import {Xtyw} from '@/view/module/xtyw.js';

export default {
  name: 'sg-base',
  components: {
    index: () => import('./components/common/index'),
    SealIndex,
    SealMain,
    SealLedger,
    AuthorizationApprovalIndex,
    AutEvaluationLedger,
    ContractProjectIndex,
    BidManagementIndex,
    BidManagementMain,
    ContractProjectMain,
    InternalProjectIndex,
    InternalProjectMain,
    ContractOppositeIndex,
    ContractOppositeMain,
    ContractApprovalIndex,
    ContractExcIndex,
    ContractExcBook,
    ContractExcDetail,
    ContractApprovalSuppleIndex,
    ContractIndex,
    ContractApprovalMain,
    ContractApprovalMainDetail,
    ContractAppSuppleMainDetail,
    ContractChartIndex,
    BlackIndex,
    BlackMain,
    BlackLedger,
    CaseIndex,
    DictIndex,
    CaseMain,
    ProcessIndex,
    CaseMainView,
    CaseProsecutionIndex,
    CaseProsecutionLedger,
    CaseProsecutionMain,
    CaseProsecutionMainDetail,
    CaseAbstract,
    CaseExamineIndex,
    CaseExamineMain,
    PreservationLedge,
    TransferIndex,
    TransferView,
    TransferMain,
    TurnoverIndex,
    TurnoverMain,
    TurnoverView,
    CaseInterfileMain,
    CaseStatistical,
    CaseLook,
    condition,
    conditionMain,
    updatecontractapi,
    updateContractMain,
    ContractTemplateIndex,
    ContractTemplateMain,
    ContractSecureMain,
    CaseReportAnnualStatistics,
    CaseReportQuarterStatistics,
    ClassCaseQuery,
    GenerateReport,
    AlphaCaseDetail,
    FirmIndex,
    LawyerIndex,
    FirmMainNew,
    FirmMainNewDetail,
    FirmMainChange,
    FirmMainChangeDetail,
    FirmMainRecommend,
    FirmMainRecommendDetail,
    MainLawyer,
    LawFirmDialog,
    LawyerDialog,
    FirmInLedger,
    FirmLedgerIndex,
    FirmLedgerLawFirm,
    FirmLedgerLawyer,
    FirmOutIndex,
    LawyerOutIndex,
    FirmOutMain,
    LawyerOutMain,
    FirmOutMainDetail,
    LawyerOutMainDetail,
    FirmOutLedger,
    FirmBlackIndex,
    LawyerBlackIndex,
    FirmBlackMain,
    LawyerBlackListMain,
    FirmBlackMainDetail,
    LawyerBlackMainDetail,
    FirmBlackLedger,
    FirmSelectIndex,
    FirmSelectMainDirect,
    FirmSelectMainDirectDetail,
    FirmSelectDirectSupplement,
    FirmSelectMainCompare,
    FirmSelectMainCompareDetail,
    FirmSelectCompareSupplement,
    FirmSelectLedgerIndex,
    FirmEvaluateIndex,
    FirmEvaluateMain,
    FirmEvaluateTaskMain,
    InternalCounselorIndex,
    InternalCounselorMain,
    LawyerUseIndex,
    LawyerUseMainDetail,
    LawyerMainNewDetail,
    CaseProjectIndex,
    CaseProjectMain,
    CaseProjectView,
    CaseLedgerIndex,
    CaseLedgerView,
    CooperationIndex,
    CooperationMain,
    CooperationView,
    CaseBaseIndex,
    CaseListpet,
    CaseListloe,
    CaseLedgerLook,
    CaseAnalysisIndex,
    CaseReportIndex,
    StandardText,
    TemplateUsageLedger,
    ContractStatistics,
    ContractFileMain,
    ContractFileIndex,
    ContractEp,
    knlIndex,
    CaseExamineMainDetail,
    CompanyContractIndex,
    GeneralContractIndex,
    message,
    page,
    commentsIndex,
    Frame,
    PerformIndex,
    PerformPayIndex,
    PerformReceivePayIndex,
    PerformResultIndex,
    PerformResultClaimIndex,
    SealTakeEffectMain,
    AutSealTakeEffectMain,
    SealStampMain,
    RemindingIndex,
    RemindingMain,
    DisputeIndex,
    DisputeMain,
    ComplianceRisksIndex,
    CaseRiskMain,
    ComplianceRisksMainDetail,
    TzxmfxMainDetail,
    TzxmfxMain,
    RiskWaringMainApproval,
    DisputeApprovalIndex,
    ProjectDemonstrationIndex,
    DisputeApprovalMain,
    DisputeApprovalMainDetail,
    DisputeApprovalLedger,
    DisputeProcessLedger,
    CaseSuedIndex,
    CaseSuedMain,
    CaseSuedMainDetail,
    CaseSuperviseIndex,
    CaseSuperviseMain,
    CaseSuperviseMainDetail,
    CaseSuperviseLedger,
    CaseLevelIndex,
    CaseLevelMain,
    CaseLevelMainDetail,
    CaseLevelLedger,
    AdminConfigIndex,
    TaskIndex,
    CaseReportMain,
    caseReportLedger,
    SealApprovalIndex,
    SealApprovalSuppleAppIndex,
    SealApprovalAppIndex,
    SealApprovalMain,
    SealApprovalMainDetail,
    SuppSealApprovalMain,
    SuppSealApprovalMainDetail,
    UserConfigIndex,
    OgnConfigIndex,
    UserPartIndex,
    ContractTypeIndex,
    BusinessTypeIndex,
    ContractOurIndex,
    StampDutyIndex,
    StampDutyMain,
    ContractCloseMain,
    AuthorizationIndex,
    AuthorizationMain,
    SublicenseMain,
    SublicenseMainDetail,
    ChangeMain,
    ChangeMainDetail,
    TerminationMain,
    TerminationMainDetail,
    AuthorizationContractMain,
    AuthorizationContractMainDetail,
    ShareholderMain,
    ShareholderMainDetail,
    LitigationMain,
    LitigationMainDetail,
    RoutineMain,
    RoutineMainDetail,
    AuthorizationLedger,
    AuthorizationMatterIndex,
    AuthorizationMainDetail,
    AutDetail,
    CenterIndex,
    ContractChangeMain,
    ContractSupplementMain,
    ContractSupplementChange,
    ContractLedgerIndex,
    RiskIndex,
    ContractEvaluateIndex,
    ContractEvaluateMain,
    ContractEvaluateDetailMain,
    ContractEvaluateTaskMain,
    ContractChangeMainDetail,
    ContractChangeMainSuppleDetail,
    ContractStopMain,
    ContractStopMainDetail,
    ContractMoreMainDetail,
    contractCloseIndex,
    ContractCloseMainDetail,
    ContractSecureMainDetail,
    UserManual,
    UserCustomMenu,
    SpecialIndex,
    SpecialMain,
    Announcement,
    AnnouncementView,
    AuthorizationMoreMainDetail,
    TzxmfxIndex,
    WgjbIndex,
    WgjbgsIndex,
    HgbgIndex,
    HgbgMain,
    HgbgMainDetail,
    HgwzIndex,
    HgwzMainDetail,
    HgpjIndex,
    HgpjtzIndex,
    HgpjtzMain,
    HgpjtzMainDetail,
    HgpjMainDetail,
    HgwhxcIndex,
    CjwttzIndex,
    CjwttzMainDetail,
    HgwhxcMainDetail,
    RiskWaringIndex,
    RiskWaringMain,
    ProjectDistribution,
    ProjectDistributionDetail,
    ResultFeedBack,
    HgwzMain,
    XtjdMain,
    HgscMain,
    HgscMainDetail,
    HgscMainHfhg,
    HgscMainDetailHfhg,
    HgscMainQsss,
    HgscMainDetailQsss,
    HgscMainTsjy,
    HgscMainDetailTsjy,
    HgscMainZdhf,
    HgscMainDetailZdhf,
    HgpxxxglMainDetail,
    HgpxxxglDetail,
    HgpxxxyhDetail,
    HgscIndex,
    CjwtallIndex,
    PublishCheckIndex,
    HgzxjhMain,
    ProjectDemonstrationMain,
    HgyxxpjMain,
    AuditTrailIndex,
    ResultsFeedbackIndex,
    RectificationPlanIndex,
    PublishCheckDetail,
    CheckResultDetail,
    RectificationPlanDetail,
    ResultFeedbackDetail,
    SelectedCheckIndex,
    AuditTrailDetail,
    CheckResultIndex,
    CheckResultMain,
    HgpxxxglIndex,
    HgpxxxyhIndex,
    HggfglIndex,
    HgzzjsIndex,
    HgryDetail,
    HgrykIndex,
    HgrykIndexRegister,
    HggfglMainDetail,
    CompolianceRiskWarning,
    ComplianceRiskWarningDetial,
    CompolianceRiskWarningMain,
    RiskWarningStandingBook,
    RiskWaringEventBook,
    RiskWarningDistribution,
    RiskWarningDataBase,
    RiskdataLedgerIndex,
    RiskWarningReceive,
    HgfxsbIndex,
    HgfxsbLedgerIndex,
    hgfxsbMainDetail,
    HgalkIndex,
    HgalkMainDetail,
    ZcfgkIndex,
    ZcfgkMainDetail,
    GwzzqdIndex,
    GwzzqdLedgerIndex,
    GwzzqdMainDetail,
    LcgkqdIndex,
    LcgkqdLedgerIndex,
    LcgkqdMainDetail,
    FxgcspIndex,
    FxgcspMainDetail,
    ProjectDemonstrationDetail,
    FxgcspMain,
    RiskApprovalProcessIndex,
    RiskApprovalProcessMainDetail,
    RiskApprovalProcessMain,
    ProbjectMajorReasoningIndex,
    ProbjectMajorReasoningMain,
    ProjectMajorReasoningApp,
    ProbjectMajorReasoningBook,
    ProjectMajorSummaryIndex,
    ProjectMajorSummaryMain,
    ProjectMajorSummaryApp,
    ProbjectMajorReasoningBookDetail,
    RiskWarningChartIndex,
    FrbbIndex,
    Frbb2,Frbb3,Frbb4,Frbb5,Frbb6,
    HgtjfxIndex,
    Xtyw,
    ProcessUserIndex,
    HgscTz,
    HgalkTz,
    ZcfgkTz,
    CaseRiskIndex,
    CaseRisksMain,
    CaseRiskMainDetail,
    CaseRiskLedger,
    ContractReport
  },

  props: {
    tabName: String,
  },
  computed: {
    componentName() {
      return this.$route.params.component.replace(/_/g, '-');
    },
    show() {
      let functionId = this.$route.query.functionId;
      if (functionId) {
        return this.tabName === this.$route.params.component + ',' + functionId;
      } else {
        return this.tabName.split(',')[0] === this.$route.params.component;
      }
    },
  },
  provide() {
    return {
      SgBasePage: this,
    };
  },
  data() {
    return {
      selectUserComponentName: 'OrgTree',
    };
  },
  methods: {
    custom(page) {
      if (page.page === 'flowMonitor') {
        if (page.type === 'transfer') {
          this.obj = {
            taskId: page.res[0].taskId,
            processInstanceId: page.flow.procInstId,
            businessKey: page.flow.businessKey,
            code: 'transfer',
          };
          processApi.flowMonitor(this.obj);
        } else if (page.type === 'jump') {
          this.obj = {
            businessKey: page.flow.businessKey,
            processInstanceId: page.flow.procInstId,
            code: 'jump',
          };
          processApi.flowMonitor(this.obj);
        }
      }
    },
  },
};
</script>

<style scoped></style>
