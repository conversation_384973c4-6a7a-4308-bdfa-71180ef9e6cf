<template>
  <div>
    <div v-if="view === 'old'">
      <div v-if="dataState !== 'view'">
        <!--  基本信息  -->
        <div style="margin: 10px">
          <span style="font-weight: bold; font-size: 16px; color: #5a5a5f">基本信息</span>
        </div>
        <el-divider></el-divider>
        <el-row style="margin-top: 10px">
          <el-col :span="8">
            <el-form-item label="立项决策" prop="projectDecisionCode">
              <el-select v-model="mainData.projectDecisionCode" clearable placeholder="请选择" style="width: 100%"
                         @change="projectDecisionChange">
                <el-option v-for="item in utils.projectDecisionData" :key="item.dicCode" :label="item.dicName"
                           :value="item.dicCode"/>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item class="custom-word-break" label="相对方确认方式" prop="relativeMethodCode">
              <span slot="label">相对方确<br/>认方式</span>
              <el-select v-model="mainData.relativeMethodCode" clearable placeholder="请选择" style="width: 100%"
                         @change="oppositePartyWayChange">
                <el-option v-for="item in utils.oppositePartyWayData" :key="item.dicCode" :label="item.dicName"
                           :value="item.dicCode"/>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="审批单号">
              <el-input v-model.trim="mainData.approvalCode" disabled maxlength="50" show-word-limit
                        style="width: 100%"/>
            </el-form-item>
          </el-col>

          <el-col :span="16">
            <el-form-item label="合同名称" prop="contractName">
              <el-input v-model.trim="mainData.contractName" clearable placeholder="请输入..."
                        show-word-limit style="width: 100%"/>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="合同编码">
              <el-input v-model.trim="mainData.contractCode" disabled maxlength="50" show-word-limit
                        style="width: 100%">
                <template slot="append" class="contractCodeAppend" style="padding: 0">
                  <span>
                 {{ mainData.version }}
                  </span>
                </template>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="合同类型" prop="contractType">
              <el-input
                  v-model.trim="mainData.contractType"
                  disabled
                  maxlength="100"
                  show-word-limit
                  style="width: 100%"/>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="自定义编码" prop="custom">
              <el-radio-group v-model="mainData.custom" @change="customUpdate">
                <el-radio :label="true" border>是</el-radio>
                <el-radio :label="false" border>否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item
                :rules="mainData.custom ? [{ required: true, message: '请输入自定义编码', trigger: 'blur' }] : []"
                label="自定义编码">
              <el-input
                  v-model="mainData.customCode"
                  :disabled="!mainData.custom"
                  clearable
                  maxlength="100"
                  placeholder="请输入内容"
                  show-word-limit/>
            </el-form-item>
          </el-col>

          <el-col :span="16">
            <el-form-item label="我方签约主体" prop="ourPartyName">
              <el-input
                  v-if="dataState !== 'view'"
                  v-model="mainData.ourPartyName"
                  class="input-with-select"
                  clearable
                  disabled
                  placeholder="请选择">
                <el-button slot="append" icon="el-icon-search" @click="contractUnitClick"/>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="我方地位" prop="ourPosition">
              <el-select v-model="mainData.ourPosition" clearable style="width: 100%">
                <el-option v-for="item in ourPositionData" :key="item.dicName" :label="item.dicName"
                           :value="item.dicName"/>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="对方签约主体" prop="otherPartyName">
              <el-input
                  v-if="dataState !== 'view'"
                  v-model="mainData.otherPartyName"
                  class="input-with-select"
                  clearable
                  disabled
                  placeholder="请选择">
                <el-button slot="append" icon="el-icon-search" @click="otherPartyClick"/>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="收支方向" prop="revenueExpenditureCode">
              <el-select v-model="mainData.revenueExpenditureCode" clearable style="width: 100%"
                         @change="revenueExpenditureChange">
                <el-option v-for="item in utils.outOrIn" :key="item.dicCode" :label="item.dicName"
                           :value="item.dicCode"/>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item
                :rules="mainData.revenueExpenditureCode !== 2 ? [{ required: true, message: '请选择金额类型', trigger: 'blur' }] : []"
                label="金额类型">
              <el-select v-model="mainData.moneyTypeCode" :disabled="mainData.revenueExpenditureCode === 2"
                         clearable style="width: 100%" @change="moneyTypeChange">
                <el-option v-for="item in utils.moneyTypeData" :key="item.dicCode" :label="item.dicName"
                           :value="item.dicCode"/>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item
                :rules="mainData.revenueExpenditureCode !== 2 ? [{ required: true, message: '请选择结算方式', trigger: 'blur' }] : []"
                label="结算方式">
              <el-select v-model="settlementMethodIds" clearable multiple placeholder="请选择" style="width: 100%"
                         @change="settlementMethodChange">
                <el-option v-for="item in settlementMethodData" :key="item.dicCode" :label="item.dicName"
                           :value="item.dicCode"/>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="合同金额" prop="contractMoney">
              <el-input-number
                  v-model="mainData.contractMoney"
                  :controls="false"
                  :max="999999999999"
                  :min="0"
                  :precision="2"
                  placeholder="请输入"
                  style="width: 100%"
                  @focus="utils.inputFocus"/>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="币种" prop="currencyCode">
              <el-select v-model="mainData.currencyCode" placeholder="请选择..." style="width: 100%;"
                         @change="currencyChange">
                <el-option v-for="item in currencyData" :key="item.dicCode" :label="item.dicName"
                           :value="item.dicCode"/>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="是否含税" prop="isTax">
              <el-radio-group v-model="mainData.isTax">
                <el-radio :label="true" border>是</el-radio>
                <el-radio :label="false" border>否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="增值税率" prop="valueAddedTaxRateCode">
              <el-select v-model="valueAddedTaxRateCodes" multiple placeholder="请选择..."
                         style="width: 100%;" @change="vatRateChange">
                <el-option v-for="item in utils.vatRateData" :key="item.dicCode" :label="item.dicName"
                           :value="item.dicCode"/>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item class="custom-word-break" label="增值税额（元）" prop="valueAddedTaxAmount">
              <!--              <span slot="label">增值税额<br/>{{'（'+mainData.currency+'）'}}</span>-->
              <el-input-number
                  v-model="mainData.valueAddedTaxAmount"
                  :controls="false"
                  :max="999999999999"
                  :min="0"
                  :precision="2"
                  placeholder="请输入"
                  style="width: 100%"
                  @focus="utils.inputFocus"/>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="汇率方式">
              <el-select v-model="mainData.exchangeRateMethodCode" placeholder="请选择..." style="width: 100%;"
                         @change="exchangeRateMethodChange">
                <el-option v-for="item in utils.exchangeRateMethodData" :key="item.dicCode" :label="item.dicName"
                           :value="item.dicCode"/>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="汇率" prop="exchangeRate">
              <el-input-number
                  v-model="mainData.exchangeRate"
                  :controls="false"
                  :disabled="mainData.currencyCode === 'CNY'"
                  :max="999999999999"
                  :min="0"
                  :precision="2"
                  placeholder="请输入"
                  style="width: 100%"
                  @focus="utils.inputFocus"/>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="合同生效日期" prop="contractTakeEffectDate">
              <el-date-picker v-model="mainData.contractTakeEffectDate" style="width: 100%;" type="date"
                              value-format="yyyy-MM-dd"/>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item class="custom-word-break" label="合同已履行金额" prop="contractExecutedMoney">
              <span slot="label">
                合同已履行金额
                <span v-if="mainData.currency" style="font-size: 10px">{{ '(' + mainData.currency + ')' }}</span>
                <el-tooltip effect="light">
                  <i class="el-icon-info"/>
                  <div
                      slot="content">该金额代表当前合同已经完成结算的实际收付款金额；若是补录变更合同，代表截至当前版本的补录合同，已经完成结算的实际收付款金额（含历史版本合同已履行的金额）。</div>
                </el-tooltip>
              </span>
              <el-input-number
                  v-model="mainData.contractExecutedMoney"
                  :controls="false"
                  :max="999999999999"
                  :min="0"
                  :precision="2"
                  maxlength="13"
                  placeholder="请输入"
                  style="width: 100%"
                  @focus="utils.inputFocus"/>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="合同标的">
              <el-input
                  v-model="mainData.contractSubjectMatter"
                  :autosize="{ minRows: 3, maxRows: 6 }"
                  maxlength="1000"
                  placeholder="请输入内容"
                  show-word-limit
                  type="textarea"/>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="摘要说明">
              <span slot="label">摘要说明</span>
              <el-input
                  v-model="mainData.summaryNote"
                  :autosize="{ minRows: 3, maxRows: 6 }"
                  maxlength="500"
                  placeholder="根据本单位业务实际，视情况注明主送领导。提交集团公司审批的业务，均需注明主送的集团领导。"
                  show-word-limit
                  type="textarea"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="合同生效文件" prop="contractTakeEffectFile">
              <uploadDoc
                  v-model="mainData.contractTakeEffectFile"
                  :disabled="dataState === 'view'"
                  :doc-path="docURL"
                  :files.sync="mainData.contractTakeEffectFile"
                  :tips="tip_"
                  accept-type=".pdf"
              />
            </el-form-item>
          </el-col>

        </el-row>

        <project
            v-if="mainData.projectDecisionCode === utils.projectDecision.JZXMLX.id || mainData.projectDecisionCode === utils.projectDecision.KYXMLX.id"
            :dataList.sync="mainData.projectList"
            :dataState="dataState"
            :dataTypeCode="mainData.dataTypeCode"
            :parentId="mainData.id"
            :projectDecisionId.sync="mainData.projectDecisionCode"></project>

        <!--关联合同-->
        <contract :dataList.sync="mainData.contractList" :dataState="dataState" :parentId="mainData.id"></contract>
      </div>

      <div v-else>
        <SimpleBoardTitle style="margin-top: 5px" title="基本信息">
          <table class="table_content" style="margin-top: 10px">
            <tbody>
            <tr>
              <th class="th_label" colspan="3">立项决策</th>
              <td class="td_value" colspan="6">{{ mainData.projectDecision }}</td>
              <th class="th_label" colspan="3">相对方确认方式</th>
              <td class="td_value" colspan="6">{{ mainData.relativeMethod }}</td>
              <th class="th_label" colspan="3">审批单号</th>
              <td class="td_value" colspan="6">{{ mainData.approvalCode }}</td>
            </tr>

            <tr>
              <th class="th_label" colspan="3">合同名称</th>
              <td class="td_value" colspan="15">{{ mainData.contractName }}</td>
              <th class="th_label" colspan="3">合同编码</th>
              <td class="td_value" colspan="6">{{ mainData.contractCodeVersion }}</td>
            </tr>

            <tr>
              <th class="th_label" colspan="3">合同类型</th>
              <td class="td_value" colspan="6">{{ mainData.contractType }}</td>
              <th class="th_label" colspan="3">自定义编码</th>
              <td class="td_value" colspan="6">{{ mainData.custom ? '是' : '否' }}</td>
              <th class="th_label" colspan="3">自定义编码</th>
              <td class="td_value" colspan="6">{{ mainData.customCode }}</td>
            </tr>

            <tr>
              <th class="th_label" colspan="3">我方签约主体</th>
              <td class="td_value" colspan="15">{{ mainData.ourPartyName }}</td>
              <th class="th_label" colspan="3">我方地位</th>
              <td class="td_value" colspan="6">{{ mainData.ourPosition }}</td>
            </tr>
            <tr>
              <th class="th_label" colspan="3">对方签约主体</th>
              <td class="td_value" colspan="24">{{ mainData.otherPartyName }}</td>
            </tr>
            <tr>
              <th class="th_label" colspan="3">收支方向</th>
              <td class="td_value" colspan="6">{{ mainData.revenueExpenditure }}</td>
              <th class="th_label" colspan="3">金额类型</th>
              <td class="td_value" colspan="6">{{ mainData.moneyType }}</td>
              <th class="th_label" colspan="3">结算方式</th>
              <td class="td_value" colspan="6">{{ mainData.settlementMethod }}</td>
            </tr>
            <tr>
              <th class="th_label" colspan="3">合同金额</th>
              <td class="td_value" colspan="6">{{ mainData.contractMoney }}</td>
              <th class="th_label" colspan="3">币种</th>
              <td class="td_value" colspan="6">{{ mainData.currency }}</td>
              <th class="th_label" colspan="3">是否含税</th>
              <td class="td_value" colspan="6">{{ mainData.isTax ? '是' : '否' }}</td>
            </tr>
            <tr>
              <th class="th_label" colspan="3">增值税率</th>
              <td class="td_value" colspan="6">{{ mainData.valueAddedTaxRate }}</td>
              <th class="th_label" colspan="3">增值税额</th>
              <td class="td_value" colspan="6">{{ mainData.valueAddedTaxAmount }}</td>
              <th class="th_label" colspan="3">汇率方式</th>
              <td class="td_value" colspan="6">{{ mainData.exchangeRateMethod }}</td>
            </tr>
            <tr>
              <th class="th_label" colspan="3">汇率</th>
              <td class="td_value" colspan="6">{{ mainData.exchangeRate }}</td>
              <th class="th_label" colspan="3">合同生效日期</th>
              <td class="td_value" colspan="6">{{ mainData.contractTakeEffectDate | date('{y}-{m}-{d}') }}</td>
              <th class="th_label" colspan="3">合同已履行金额</th>
              <td class="td_value" colspan="6">{{ mainData.contractExecutedMoney }}</td>
            </tr>

            <tr>
              <th class="th_label" colspan="3">合同标的</th>
              <td class="td_value_" colspan="24">{{ mainData.contractSubjectMatter }}</td>
            </tr>
            <tr>
              <th class="th_label" colspan="3">摘要说明</th>
              <td class="td_value_" colspan="24">{{ mainData.summaryNote }}</td>
            </tr>
            </tbody>
            <tbody>
            <tr>
              <th class="th_label" colspan="3">合同生效文件</th>
              <td class="td_value" colspan="24">
                <uploadDoc
                    v-model="mainData.contractTakeEffectFile"
                    :disabled="isView"
                    :doc-path="'/effect'"
                    :files.sync="mainData.contractTakeEffectFile"/>
              </td>
            </tr>
            </tbody>
          </table>
          <project
              v-if="mainData.projectDecisionCode === utils.projectDecision.JZXMLX.id || mainData.projectDecisionCode === utils.projectDecision.KYXMLX.id"
              :dataList.sync="mainData.projectList"
              :dataState="dataState"
              :dataTypeCode="mainData.dataTypeCode"
              :parentId="mainData.id"
              :projectDecisionId.sync="mainData.projectDecisionCode"></project>
          <!--关联合同-->
          <contract :dataList.sync="mainData.contractList" :dataState="dataState" :parentId="mainData.id"></contract>

        </SimpleBoardTitle>

      </div>

      <el-row class="rowCol1" style="margin-top: 20px">
        <el-col :span="12">
          <el-form-item label="经办单位">
            <span class="viewSpan">{{ mainData.createOgnName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="经办人">
            <span class="viewSpan">{{ mainData.createPsnName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="移交人">
            <span class="viewSpan">{{ mainData.createTransferName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经办部门">
            <span class="viewSpan">{{ mainData.createDeptName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="经办时间">
            <span class="viewSpan">{{ mainData.createTime | parseTime }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="移交时间">
            <span class="viewSpan">{{ mainData.createTransferTime | parseTime }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <!--合同类型-->
      <TypeDialog :dialogVisible.sync="typeTreeDialog" @onSure="onSure"></TypeDialog>
      <!--选择内部单位-->
      <el-dialog :close-on-click-modal="false" :visible.sync="orgVisible" title="选择部门" width="50%">
        <div class="el-dialog-div">
          <orgTree
              :accordion="false"
              :checked-data.sync="zxcheckedData"
              :is-check="true"
              :is-filter="true"
              :is-not-cascade="true"
              :is-ogn="true"
              :otherHeight="280"
              :show-user="false"/>
        </div>
        <span slot="footer" class="dialog-footer">
					<el-button class="negative-btn" icon="" @click="orgVisible = false">取消</el-button>
					<el-button class="active-btn" icon="" type="primary" @click="orgSure_">确定</el-button>
				</span>
      </el-dialog>
      <!--选择内部客商-->
      <OppositeDialog :is-multiple="true" :oppositeDialogVisible.sync="oppositeDialogVisible"
                      @oppositeSure_="oppositeSure_"></OppositeDialog>
    </div>
  </div>
</template>

<script>
import uploadDoc from '@/view/components/UploadDoc/UploadDoc';
import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut';
import contract from '../child/contract';
import standardText from '../child/standardText';
import TypeDialog from '@/view/litigation/contractManage/contractTypeManage/TypeDialog';
import orgTree from '@/view/components/OrgTree/OrgTree'
import SimpleBoardTitle from '@/view/components/SimpleBoard/SimpleBoardTitle';
import {mapGetters} from 'vuex';
import OppositeDialog from "@/view/litigation/contractManage/contractApproval/dialog/BusinessDialog.vue";
import SectionDialog from '../dialog/SectionDialog.vue';
import project from "@/view/litigation/contractManage/contractApproval/child/BmProject.vue";

export default {
  name: 'Generic',
  inject: ['layout', 'mcpLayout'],
  components: {
    project,
    uploadDoc,
    Shortcut,
    contract,
    TypeDialog,
    orgTree,
    SimpleBoardTitle,
    standardText,
    OppositeDialog,
    SectionDialog,
  },
  props: {
    data: {
      type: Object,
      default: function () {
        return {};
      },
    },
    ourPositionList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    dataState: {
      type: String,
      default: '',
    },
    procInstId: {
      type: String,
      default: null,
    },
    taskId: {
      type: String,
      default: null,
    },
    view: {
      type: String,
      default: 'new',
    },
    create: {
      type: String,
      default: '',
    },
  },
  computed: {
    ...mapGetters(['orgContext', 'currentFunctionId']),
    isView: function () {
      return this.dataState === this.utils.formState.VIEW;
    },
    isCreate: function () {
      return this.create === 'create';
    },
    isNotice() {
      const isNotice = this.$route.query.isNotice;
      return (
          this.mainData.dataStateCode !== this.utils.dataState_BPM.FINISH.code &&
          this.mainData.createPsnFullId === this.orgContext.currentPsnFullId &&
          isNotice
      );
    },

    settlementMethodIds: {
      set: function (data) {
        this.mainData.settlementMethodCode = data.join(',');
      },
      get: function () {
        if (this.mainData.settlementMethodCode) {
          return this.mainData.settlementMethodCode.split(',');
        }
        return [];
      },
    },
    valueAddedTaxRateCodes: {
      set: function (data) {
        this.mainData.valueAddedTaxRateCode = data.join(',');
      },
      get: function () {
        if (this.mainData.valueAddedTaxRateCode) {
          return this.mainData.valueAddedTaxRateCode.split(',');
        }
        return [];
      },
    },
    isZCIncluded() {
      return this.mainData?.contractTypeCode?.includes('ZC') || false;
    }
  },
  watch: {
    data: {
      handler(val, old) {
        this.mainData = val;
      },
      deep: true,
    },
    ourPositionList: {
      handler(val, old) {
        this.ourPositionData = val;
      },
      deep: true,
    },
  },
  data() {
    return {
      printType: false,
      docURL: '/contract', // 测试暂时的
      tip_: "支持多种格式上传，如doc、pdf、zip等",
      functionId: null, //终止的时候要用，需要手动关闭
      typeTreeDialog: false,
      oppositeDialogVisible: false,
      authorizationVisible: false,
      sectionVisible: false,
      mainData: {},
      obj: {},
      contractDialogVisible: false,
      ourPositionData: [],
      settlementMethodData: [],
      currencyData: [],
      orgVisible: false,
      zxcheckedData: [],
      businessTypeData: [],
      surplusMoney: null,
      jsonValue: null,
    };
  },
  created() {
    this.initDic();
    this.refreshData();
  },
  methods: {
    initDic() {
      // 相对方确定方式
      const codes = ['HTQC_JSFS', 'HTQC_BZ', 'HTQC_ZCYWLX']
      this.utils.getDic(codes).then((response) => {
        this.settlementMethodData = response.data.data[codes[0]];
        this.currencyData = response.data.data[codes[1]];
        this.businessTypeData = response.data.data[codes[2]];
      });

    },
    projectDecisionChange(val) {
      if (val !== 1 && val !== 2) {
        this.mainData.projectList = []
      }
      this.mainData.projectDecision = this.utils.getDicName2(this.utils.projectDecisionData, val);
    },
    oppositePartyWayChange(val) {
      this.mainData.relativeMethod = this.utils.getDicName2(this.utils.oppositePartyWayData, val);
    },
    settlementMethodChange(val) {
      if (val && val.length > 0) {
        let arr = [];
        for (let i = 0; i < val.length; i++) {
          const newValElement = val[i];
          const name = this.utils.getDicName2(this.settlementMethodData, newValElement);
          arr.push(name);
        }
        this.mainData.settlementMethod = arr.join('、');
      }
    },
    revenueExpenditureChange(val) {
      if (val === 2) {
        this.mainData.moneyTypeCode = 2
        this.mainData.moneyType = '其他类型'
      }
      this.mainData.revenueExpenditure = this.utils.getDicName2(this.utils.outOrIn, val);
    },
    moneyTypeChange(val) {
      this.mainData.moneyType = this.utils.getDicName2(this.utils.moneyTypeData, val);
    },
    vatRateChange(val) {
      if (val && val.length > 0) {
        let arr = [];
        for (let i = 0; i < val.length; i++) {
          const newValElement = val[i];
          const name = this.utils.getDicName2(this.utils.vatRateData, newValElement);
          arr.push(name);
        }
        this.mainData.valueAddedTaxRate = arr.join('、');
      }
    },
    currencyChange(val) {
      if (val === 'CNY') {
        this.mainData.exchangeRate = 1;
      }
      this.mainData.currency = this.utils.getDicName2(this.currencyData, val);
    },
    exchangeRateMethodChange(val) {
      this.mainData.exchangeRateMethod = this.utils.getDicName2(this.utils.exchangeRateMethodData, val);
    },
    onSure(data) {
      this.mainData.twoTypeName = data.typeName;
      this.mainData.twoTypeCode = data.typeCode;
      this.mainData.oneTypeName = data.parentName;
      this.mainData.oneTypeCode = data.parentCode;
    },
    surplusMoneyAttribute(data) {
      this.surplusMoney = data;
    },
    refreshData() {
      // contractApi
      //     .queryProcessOpinion({
      //       processInstanceId: this.$route.query.processInstanceId,
      //       taskId: this.$route.query.taskId,
      //     })
      //     .then((response) => {
      //       this.jsonValue = response.data.data;
      //     });
    },
    //选择我方签约主体
    contractUnitClick() {
      this.orgVisible = true
    },
    orgSure_() {
      let c = ''
      let cid = ''
      const res = this.zxcheckedData[0]

      if (res.unitType !== "ogn" && res.unitType !== "branch") {
        this.$message.error("请选择法人单位或分公司")
      } else {
        this.zxcheckedData.forEach((item) => {
          if (c.length === 0) {
            c = c + item.name
            cid = cid + item.unitId
          } else {
            c = c + ',' + item.name
            cid = cid + ',' + item.unitId
          }
        })
        this.mainData.ourPartyName = c
        this.mainData.ourPartyList = cid
      }
      this.orgVisible = false
    },
    otherPartyClick() {
      this.oppositeDialogVisible = true
    },
    oppositeSure_(data) {
      console.log("内部单位：")
      console.log(data.data)

      console.log("内部员工：")
      console.log(data.data1)

      console.log("外部客商：")
      console.log(data.data2)
      var c = ''
      var cid = ''
      if (data.data !== undefined && data.data != null && data.data.length > 0) {
        data.data.forEach((item) => {
          if (c.length === 0) {
            c = c + item.name
            cid = cid + item.unitId
          } else {
            c = c + ',' + item.name
            cid = cid + ',' + item.unitId
          }
        })
      }
      if (data.data1 !== undefined && data.data1 != null && data.data1.length > 0) {
        data.data1.forEach((item) => {
          if (c.length === 0) {
            c = c + item.staffName
            cid = cid + item.staffCode
          } else {
            c = c + ',' + item.staffName
            cid = cid + ',' + item.staffCode
          }
        })
      }
      if (data.data2 !== undefined && data.data2 != null && data.data2.length > 0) {
        data.data2.forEach((item) => {
          if (c.length === 0) {
            c = c + item.businessName
            cid = cid + item.code
          } else {
            c = c + ',' + item.businessName
            cid = cid + ',' + item.code
          }
        })
      }
      this.mainData.otherPartyName = c
      this.mainData.otherPartyList = cid
      this.oppositeDialogVisible = false
    },
    authorizationSure(val) {
      this.mainData.authorizedName = val.authorizedName
      this.mainData.authorizedCode = val.authorizedBookNumber
      this.mainData.authorizedPerson = val.trustee
      this.mainData.entrustedRange = val.entrustedRange
      this.authorizationVisible = false;
    },
    sectionSure(val) {
      this.mainData.section = val.sectionPackageName
      this.mainData.sectionCode = val.sectionPackageCode
      this.mainData.otherPartyList = val.successfulSupplierCode
      this.mainData.otherPartyName = val.successfulSupplierName
      this.mainData.contractMoney = val.contractPrice
      this.mainData.isTax = val.isTaxIncluded !== '不含税'
      this.mainData.currencyCode = val.currency
      this.mainData.currency = this.utils.getDicName2(this.currencyData, val.currency);
      this.mainData.ourPartyName = val.procurementUnitName
      this.mainData.ourPartyList = val.procurementUnitCode
      this.sectionVisible = false;
    },
    customUpdate(val) {
      if (!val) {
        if (this.mainData.contractCode != null) {
          this.mainData.customCode = this.mainData.contractCode
        } else {
          this.mainData.customCode = null
        }
      }
    },
  }
}
</script>

<style>
.mylabel .el-form-item .el-form-item__label {
  line-height: 15px;
}

.upClass .el-upload-list__item {
  margin-bottom: 3px !important;
}
</style>

<style scoped>
.td_value_approval_2 {
  height: 60px;
  font-size: 15px;
  border: solid 1px #606266;
  padding-left: 10px;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  word-wrap: break-word;
  text-align: left;
  color: gray;
}

.el-row {
  display: flex;
  flex-wrap: wrap;
}

.rowCol1 .el-form-item {
  margin-bottom: 0 !important;
}

/deep/ input[aria-hidden="true"] {
  display: none !important;
}

/deep/ .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
  box-shadow: none !important;
}


</style>
