import Vue from 'vue';
import Router from 'vue-router';
import rootPath from './rootPath'
import design_pages from '../components/DesignPages'
import transfer from '../components/Transfer'
import logout from '../components/logout'
Vue.use(Router);
// 解决ElementUI导航栏中的vue-router在3.0版本以上重复点菜单报错问题
const originalPush = Router.prototype.push
Router.prototype.push = function push(location) {
	return originalPush.call(this, location).catch(err => err)
}


// import Layout from '../components/Layout.vue'
const LOGIN = () => import('../components/login.vue');
const CENTER = () => import('../view/litigation/center/index.vue');
const ssoLogin = () => import('../components/ssoLogin');
const NO_VISIT = () => import('../components/common/403.vue');
const NOT_FOUND = () => import('../components/common/404.vue');
const SEVER_ERROR = () => import('../components/common/500.vue')
const accessDenied = () => import('../components/common/accessDenied.vue')
const TzxmfxMainDetailnied = () => import('../view/Compliance/Risk/tzxmfx/TzxmfxMainDetail.vue')
import fullPath from './fullPath'
import contract from '../view/router/contract'
import case_ from '../view/router/case'
import lawFirm from '../view/router/lawFirm'
import center from '../view/router/center'
import report from '../view/router/report'
let baseRoute = [
	{path: `${rootPath.root}/login`, name: 'login', component: LOGIN},
	{path: `/base/centerIndex`, name: 'centerIndex', component: CENTER},
	{path: `${rootPath.root}/ssoLogin`, name: 'ssoLogin', component: ssoLogin},
	{path: `${rootPath.root}/fw/ssologin`, name: 'ssoLogin', component: ssoLogin},
	{path: `${rootPath.root}/design_pages`, name: 'design_pages', component: design_pages},
    {path: `${rootPath.root}/transfer`, name: 'transfer', component: transfer},
	{path: `${rootPath.root}/logout`, name: 'logout', component: logout},
    {path: `${rootPath.root}/403`,name: 'haveNoRight',component: NO_VISIT},
	{path: `${rootPath.root}/404`,name: 'notFound',component: NOT_FOUND},
	{path: `${rootPath.root}/500`,name: 'sererError',component: SEVER_ERROR},
	{path: `${rootPath.root}/accessDenied`,name: 'accessDenied',component: accessDenied},
		...fullPath,...contract,...case_,...lawFirm,...center,...report,
	{
		path: '*',
		redirect: '/404'
	}
];


let router = new Router({
	routes: baseRoute,
	mode: 'history'
});
export default router;
