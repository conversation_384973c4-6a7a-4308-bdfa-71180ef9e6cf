<template>
  <el-card class="simple-board-index" shadow="never">
    <div slot="header">
      <div v-show="hasValue || showContent ">
        <div @click="extendSlot">
          <span style="font-weight: bold;font-size: 16px;color: #5A5A5F">{{ title }}</span>
          <span v-if="isBody" style="color: red;font-weight: bolder">
            &nbsp;{{ body }}
          </span>
          <slot name="left"/>
          <div v-if="isHas && !isView" class="checkValue">
            <el-switch
                v-model="hasValueQ"
                :disabled="isView"
                @change="handleUpdate">
            </el-switch>
          </div>
          <span v-if="utils.isNotNullString(msg)" class="tips">{{ msg }}</span>
          <div v-if="!isView && hasAdd" style="float: right;display: inline;margin-top: -3px;">
            <el-button v-if="isButton" class="normal-btn" size="mini" icon="el-icon-plus" @click="addBtn">新增
            </el-button>
            <el-button v-if="isFirmIndex" class="normal-btn" size="mini" icon="el-icon-plus" @click="addFormalFirm">
              正式入库
            </el-button>
            <el-button v-if="isFirmIndex" class="normal-btn" size="mini" icon="el-icon-plus" @click="addTemporaryFirm">
              临时入库
            </el-button>
            <div v-if="isDropdown">
              <el-dropdown trigger="click" size="small" placement="bottom-start" @command="addBtn">
                <el-button type="primary" class="normal-btn" size="mini">新增<i
                    class="el-icon-arrow-down el-icon--right"></i></el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="item in dropdowns" :key="item.id" icon="el-icon-circle-plus-outline"
                                    :command="item.id">{{ item.dicName }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <slot name="button"/>
          </div>
          <span v-show="!visible && isView" style="float: right;cursor:pointer;font-weight:bolder;"><i
              class="el-icon-arrow-down"/></span>
          <span v-show="visible && isView" style="float: right;cursor:pointer;font-weight:bolder;"><i
              class="el-icon-arrow-up"/></span>
        </div>
      </div>
      <div v-show="!hasValue && !showContent ">
        <el-row>
          <el-col :span="24">
            <div v-if="dataState==='view'">
              <span style="font-weight: bold;font-size: 14px;color: #5A5A5F">{{ title + '&nbsp;&nbsp;' }}</span><span
                class="viewSpan">{{ '暂无数据' }}</span>
            </div>
            <span v-else style="font-weight: bold;font-size: 14px;color: #5A5A5F">{{ title }}</span>
            <div v-if="isHas && !isView" class="checkValue">
              <el-switch
                  v-model="hasValueQ"
                  :disabled="isView"
                  @change="handleUpdate2"
              ></el-switch>
            </div>
            <span v-if="utils.isNotNullString(msg)" class="tips">{{ msg }}</span>
          </el-col>
        </el-row>
      </div>
    </div>

    <div v-show="hasValue || showContent " style="padding-left: 10px;padding-right: 10px">
      <!--<el-divider v-if="visible"></el-divider>-->
    </div>

    <div v-show="hasValue || showContent ">
      <div v-if="visible" class="contentIndex">
        <slot/>
      </div>
    </div>
  </el-card>
</template>

<script>
export default {
  name: 'SimpleBoardIndex',
  props: {
    title: {
      type: String,
      default: '标题'
    },
    hasAdd: {// 是否显示按钮
      type: Boolean,
      default: true
    },
    isHas: {//是否显示开关，
      type: Boolean,
      default: false
    },
    hasValue: {// 填充内容条数是否大于0
      type: Boolean,
      default: true
    },
    dataState: {// 当前表单状态
      type: String,
      default: ''
    },
    msg: {// 【注意】内容
      type: String,
      default: ''
    },
    noInfo: {
      type: String,
      default: '暂无信息'
    },
    isButton: {// 是否是新增按钮
      type: Boolean,
      default: false
    },
    isFirmIndex: {// 是否是律所首页
      type: Boolean,
      default: false
    },
    isDropdown: {// 是否是下拉按钮
      type: Boolean,
      default: false
    },
    dropdowns: {// 下拉按钮显示内容
      type: Array,
      default: function () {
        return []
      }
    },
    isBody: {
      type: Boolean,
      default: false
    },
    body: {
      type: String,
      default: ''
    },
    visible: {// 当前是折叠状态 还是 展开状态，控制图标
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      hasValueQ: this.hasValue,
      showContent: false
    }
  },
  computed: {
    isView: function () {
      return this.dataState === this.utils.formState.VIEW
    }
  },
  watch: {
    hasValue(val) {
      this.hasValueQ = this.showContent ? true : val
    },
    hasValueQ(val) {
      if (val === true) {
        this.handleUpdate2(true)
      }
    }
  },
  methods: {
    extendSlot() {
      if (this.dataState === 'view' || (this.dataState !== 'view' && this.visible === false)) {
        this.visible = !this.visible
      }
    },
    addBtn(event) {
      this.$emit('addBtn', event)
    },
    addFormalFirm(event) {
      this.$emit('addFormalFirm', event)
    },
    addTemporaryFirm(event) {
      this.$emit('addTemporaryFirm', event)
    },
    handleUpdate(value) {
      if (!value) {
        this.hasValueQ = !value
        this.$confirm('已填写数据将被清空，是否确定？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          this.hasValueQ = value
          this.showContent = false
          this.$emit('deleteData')
        }).catch(action => {
        })
      }
    },
    handleUpdate2(val) {
      this.showContent = true
      this.hasValueQ = true
    }
  }
}
</script>

<style>
.simple-board-index {
  margin: 0 0;
}

.simple-board-index .leftTips {
  /*line-height: 30px;
  !*padding: .1rem 1.5rem;*!
  border-left-width: .5rem;
  border-left-style: solid;
  margin: 1rem 0;
 !* background-color: #f3f5f7;*!
  border-color: #42b983;
  padding-top: 5px;
  padding-bottom: 5px;
  padding-left: 10px;*/

  padding: 8px 16px;
  background-color: #ecf8ff;
  border-radius: 4px;
  border-left: 5px solid #50bfff;
  margin: 20px 0 0 0;
  font-size: 15px;
  font-weight: bold;
}

.simple-board-index .secondLeftTips {
  /*line-height: 30px;
  !*padding: .1rem 1.5rem;*!
  border-left-width: .5rem;
  border-left-style: solid;
  margin: 1rem 0;
 !* background-color: #f3f5f7;*!
  border-color: #42b983;
  padding-top: 5px;
  padding-bottom: 5px;
  padding-left: 10px;*/

  padding: 5px 10px 5px;
  background-color: #ffffff;
  border-radius: 4px;
  border-left: 5px solid #ffffff;
  margin: 0 0 0 0;
  font-size: 15px;
  font-weight: bold;
}

.checkValue {
  padding-left: 20px;
  display: inline;
}

.contentIndex {
  /*margin-top: 0px;*/
  /*padding-left: 10px;*/
  /*padding: 10px;*/
}

.simple-board-index .tips {
  color: red;
  margin-left: 10px;
  font-weight: 400;
  font-size: 14px;
}

.leftTips .title {
  font-size: 16px;
  font-weight: bold;
}

.el-divider--horizontal {
  margin: 0;
}

.el-divider {
  background-color: #EEEEEE;
}
</style>

<style scoped lang="scss">
.simple-board-index /deep/ .el-card__body {
  padding: 0 !important;
}

.simple-board-index /deep/ .el-card__header {
  background-color: #FAFAFA;
  border-bottom: 0;
}
</style>
