<template>
  <FormWindow ref="formWindow" @initData="initData" @loadData="loadData">
    <el-container style="height: calc(100vh - 84px)">
      <!--编辑页面  内部查看页面-->
      <el-main>
        <el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
          <el-row
              style="padding: 10px 0 10px 0; position: fixed; width: 100%; overflow-y: auto; background-color: white; z-index: 999">
            <el-button v-if="!isView" type="primary" size="mini" @click="save_">保存</el-button>
            <el-button v-if="!isView" type="success" size="mini" @click="approval_">提交</el-button>
            <el-button v-if="!isView" type="primary" size="mini" @click="uploadFiles">附件</el-button>
          </el-row>
          <div style="padding-top: 50px"></div>
          <span style="text-align: left; font-size: 23px; margin-left: 43%; font-weight: 900">合同变更详情单</span>
          <el-form
              ref="dataForm"
              :model="mainData"
              :rules="!isView ? rules : {}"
              label-width="100px"
              :style="mainData.dataStateCode === utils.dataState_BPM.FINISH.code ? 'margin-right: 50px;' : ' margin-right: 10px;'">
            <!--通用合同-->
            <generic ref="generic" :data="mainData" :ourPositionList="ourPositionData" :dataState="dataState"
                     :view="view"></generic>
            <el-dialog title="合同附件" :visible.sync="uploadDialogVisible" :close-on-click-modal="false" width="70%"
                       class="myDialog">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="上传文件">
                    <uploadDoc
                        v-model="mainData.contractFiles"
                        :files.sync="mainData.contractFiles"
                        :disabled="isView"
                        :doc-path="'/contract'"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <div slot="footer" class="dialog-footer">
                <el-button size="mini" type="primary" @click="uploadDialogVisible = false">确定</el-button>
              </div>
            </el-dialog>
          </el-form>
        </el-scrollbar>
      </el-main>
    </el-container>
  </FormWindow>
</template>

<script>
import FormWindow from '@/view/components/FormWindow/FormWindow';
import generic from './change/ContractGenericSupple';
import contractApi from '@/api/contract/bmContract';
import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut';
import {mapGetters} from 'vuex';
import SimpleBoardTitleApproval from '@/view/components/SimpleBoard/SimpleBoardTitleApproval';
import ourApi from "@/api/contract/contractOur";
import UploadDoc from "@/view/components/UploadDoc/UploadDoc.vue";

	export default {
		name: 'ContractChangeMainDetail',
    inject: ['layout', 'mcpLayout'],
    components: {UploadDoc, FormWindow, generic, Shortcut, SimpleBoardTitleApproval},
    computed: {
      ...mapGetters(['orgContext', 'currentFunctionId']),
      isView: function () {
        return this.dataState === this.utils.formState.VIEW;
      },
    },
    watch: {
    },
    provide() {
      return {
        parentContract: this,
      };
    },
    data() {
      return {
        type: null,
        view: 'old',
        functionId: null, //终止的时候要用，需要手动关闭
        dataState: null, //表单状态，新增、查看、编辑
        loading: true, //查看时，加载中的动画
        uploadDialogVisible: false, //查看时，加载中的动画
        isCheckSuccess: null,
        mainData: {
          id: null,
          takeEffectName: '待用印',//生效状态编码
          takeEffectCode: 1,//生效状态
          performState: '未履行',//履行状态
          performStateCode: 1,//履行状态编码
          performanceState: '未计划',//履行计划状态
          performanceStateCode: 1,//履行计划状态编码
          changeState: '未变更',//变更状态
          changeStateCode: 1,//变更状态编码
          closeState: '未关闭',//关闭状态
          closeStateCode: 1,//关闭状态编码
          evaluatedState: '未评价',//评价状态
          evaluatedStateCode: 1,//评价状态编码
          archivedState: '未归档',//归档状态
          archivedStateCode: 1,//归档状态编码
          dataTypeName: '补录变更',//合同性质
          dataTypeCode: 5,//合同性质编码
          dataSource: '法务系统',//数据来源
          dataSourceCode: '100008',//数据来源编码
          dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
          dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码
          approvalCode: null,//审批单号
          contractName: null,//合同名称
          contractCode: null,//合同编码
          version: null,//版本号
          standardAttachmentVersion: 0,//范本版本号
          contractType: null,//合同类型
          contractTypeCode: null,//合同类型编码
          contractTypeLevel: null,//合同类型
          custom: false,//是否自定义编码
          customCode: null,//自定义编码值
          ourPartyName: null,//我方签约主体
          ourPartyList: null,//我方签约主体编码
          ourPosition: null,//我方地位
          otherPartyName: null,//对方签约主体
          otherPartyList: null,//对方签约主体编码
          moneyType: null,//金额类型
          moneyTypeCode: null,//金额类型编码
          settlementMethod: null,//结算方式
          settlementMethodCode: null,//结算方式Code
          revenueExpenditure: null,//收支方向
          revenueExpenditureCode: null,//收支方向编码
          contractMoney: null,//合同金额
          isTax: false,//是否含税
          valueAddedTaxRate: null,//增值税率
          valueAddedTaxRateCode: null,//增值税率编码
          valueAddedTaxAmount: null,//增值税额
          currency: null,//币种
          currencyCode: null,//币种编码
          exchangeRateMethod: null,//汇率方式
          exchangeRateMethodCode: null,//汇率方式编码
          exchangeRate: null,//汇率
          agreedStartTime: null,//合同约定开始时间
          agreedEndTime: null,//合同约定结束时间
          whetherGroupMajor: false,//集团重大合同
          whetherUnitMajor: false,//本单位重大合同
          authorizedSource: null,//授权来源
          authorizedCode: null,//授权编号
          authorizedId: null,//授权ID
          authorizedName: null,//授权名称
          authorizedPerson: null,//被授权人
          authorizedRange: null,//授权范围
          contractSubjectMatter: null,//合同标的
          summaryNote: null,//摘要说明
          projectDecision: null,//立项决策
          projectDecisionCode: null,//立项决策编码
          relativeMethod: null,//相对方确认方式
          relativeMethodCode: null,//相对方确认方式编码
          contractCodeVersion: null,//拼接合同编码
          authorizedPersonId: null,//被授权人ID
          isAtemplate: false,//是否范本
          originalContractName: null,//原合同名称
          originalContractCode: null,//原合同编码
          originalContractId: null,//原合同ID
          originalContractMoney: null,//原合同金额
          originalValueAddedTaxAmount: null,//原增值税额
          changeMoneyType: null,//金额变更类型
          changeMoneyTypeCode: null,//金额变更类型编码
          thisChangeMoney: undefined,//本次变更金额
          thisChangeValueAddedTaxAmount: undefined,//本次变更增值税金额
          afterChangeMoney: undefined,//变更后合同金额
          afterChangeValueAddedTaxAmount: undefined,//变更后增值税金额
          contractExecutoryMoney: null,//合同待履行金额
          contractExecutedMoney: undefined,//已履行金额
          contractTakeEffectDate: null,//生效时间
          closeId: null,//关闭ID
          explain: null,//合同终止说明
          contractFiles: null,//合同附件
          createOgnId: null,
          createOgnName: null,
          createDeptId: null,
          createDeptName: null,
          createPsnId: null,
          createPsnName: null,
          createPsnFullId: null,
          createPsnFullName: null,
          createPsnPhone: null,
          createGroupId: null,
          createGroupName: null,
          createOrgId: null,
          createOrgName: null,
          createLegalUnitId: null,
          createLegalUnitName: null,
          createTime: null,
          projectList: [],// 所属项目
          effectList: [],// 生效信息
          textList: [],// 文本信息
          sealList: [],// 关联用印
          riskList: [],// 风控信息
          contractList: [],// 关联合同
        },
        rules: {
          originalContractName: [{required: true, message: '请选择原合同', trigger: 'blur'}],
          projectDecisionCode: [{required: true, message: '请选择立项决策', trigger: 'blur'}],
          relativeMethodCode: [{required: true, message: '请选择相对方确定方式', trigger: 'blur'}],
          contractName: [{required: true, message: '请输入合同名称', trigger: 'blur'}],
          contractType: [{required: true, message: '请选择合同类型', trigger: 'blur'}],
          custom: [{required: true, message: '请选择自定义编码', trigger: 'blur'}],
          ourPartyName: [{required: true, message: '请选择我方签约主体', trigger: 'blur'}],
          ourPosition: [{required: true, message: '请选择我方地位', trigger: 'blur'}],
          otherPartyName: [{required: true, message: '请选择对方签约主体', trigger: 'blur'}],
          moneyTypeCode: [{required: true, message: '请选择金额类型', trigger: 'blur'}],
          settlementMethodCode: [{required: true, message: '请选择结算方式', trigger: 'blur'}],
          revenueExpenditureCode: [{required: true, message: '请选择收支方向', trigger: 'blur'}],
          contractMoney: [{required: true, message: '请输入合同金额', trigger: 'blur'}],
          currencyCode: [{required: true, message: '请选择币种', trigger: 'blur'}],
          isTax: [{required: true, message: '请选择是否含税', trigger: 'blur'}],
          valueAddedTaxRateCode: [{required: true, message: '请选择增值税率', trigger: 'blur'}],
          exchangeRate: [{required: true, message: '请选择汇率', trigger: 'blur'}],
          whetherGroupMajor: [{required: true, message: '请选择集团重大合同', trigger: 'blur'}],
          authorizedSource: [{required: true, message: '请选择授权来源', trigger: 'blur'}],
          authorizedBookNumber: [{required: true, message: '请选择授权书号', trigger: 'blur'}],
          changeMoneyTypeCode: [{required: true, message: '请选择变更金额类型', trigger: 'blur'}],
          contractTakeEffectFile: [{required: true, message: '请上传生效文件', trigger: 'blur'}],
          contractTakeEffectDate: [{required: true, message: '请选择生效时间', trigger: 'blur'}],
          thisChangeMoney: [
            {
              required: true,
              message: '请输入本次变更金额',
              trigger: 'blur',
              validator: (rule, value, callback) => {
                if (this.mainData.changeMoneyTypeCode !== 2 && ( value === null || value === undefined )) {
                  callback(new Error('请输入本次变更金额'));
                } else {
                  callback();
                }
              }
            }
          ],
          thisChangeValueAddedTaxAmount: [
            {
              required: true,
              message: '请输入本次变更增值税金额',
              trigger: 'blur',
              validator: (rule, value, callback) => {
                if (this.mainData.isTax) {
                  if (this.mainData.changeMoneyTypeCode !== 2 && ( value === null || value === undefined )) {
                    callback(new Error('请输入本次变更增值税金额'));
                  } else {
                    callback();
                  }
                }else{
                  callback();
                }
              }
            }
          ],
          contractExecutedMoney: [{required: true, message: '请输入合同已履行金额', trigger: 'blur'}],
        },
        activity: null, //记录当前待办处于流程实例的哪个环节
        obj: {
          // 流程处理逻辑需要的各种参数
          taskId: null,
          processInstanceId: null,
          businessKey: null,
          title: null,
          functionName: null,
          sid: null,
        },
        noticeParams: {},
        noticeData: {
          moduleName: '', // 模块名称
          dataId: '', // 数据ID
          url: '', // 地址
          title: '', // 地址
          params: {}, // 其他参数
        },
        ourPositionData: [],
      };
    },
    methods: {
      initData(temp, dataState) {
        this.dataState = dataState;
        Object.assign(this.mainData, temp);
        this.mainData.contractTypeCode = this.$route.query.contractTypeCode;
        this.mainData.contractType = this.$route.query.contractType;
        this.mainData.contractTypeLevel = this.$route.query.level;
        if (this.mainData.contractTypeCode != null) {
          this.getOurPosition()
        }
        const year = new Date().getFullYear();
        this.utils.createKvsequence('HTSP' + year, 6).then((value) => {
          this.mainData.approvalCode = value.data.kvsequence;
        });
      },
      loadData(dataState, dataId) {
        this.functionId = this.$route.query.functionId;
        this.dataState = dataState;
        if (this.$route.query.type !== undefined && this.$route.query.type !== '') this.type = this.$route.query.type;
        contractApi.queryById({
          id: dataId,
        }).then((res) => {
          this.mainData = res.data.data;
          this.getOurPosition()
        });
      },
      async save_() {
        if (this.mainData.originalContractCode == null) {
          this.$message.warning("请选择原合同")
          return
        }
        this.save().then((response) => {
          if (response.data.data.state === 'fail') {
            this.$message({
              message: response.data.data.message,
              dangerouslyUseHTMLString: true,
              showClose: true,
              type: 'warning',
            });
          } else {
            if (response.data.data.customCode !== undefined && response.data.data.customCode) {
              this.mainData.customCode = response.data.data.customCode;
            }
            if (response.data.data.contractCode !== undefined && response.data.data.contractCode) {
              this.mainData.contractCode = response.data.data.contractCode;
              this.mainData.version = response.data.data.version;
              this.mainData.contractCodeVersion = response.data.data.contractCodeVersion;
            }
            this.$message.success('保存成功!');
          }
        });
      },
      save() {
        return new Promise((resolve, reject) => {
          console.log('执行save');
          this.mainData.dataTypeName = '补录变更';
          this.mainData.dataTypeCode = 5;
          contractApi.save(this.mainData).then((response) => {
            resolve(response);
          }).catch((error) => {
            reject(error);
          });
        });
      },
      approval_() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            let bool = false;
            //项目信息的校验
            if (this.mainData.projectDecisionCode === this.utils.projectDecision.JZXMLX.id ||
                this.mainData.projectDecisionCode === this.utils.projectDecision.KYXMLX.id) {
              //项目信息不能为空
              if (this.mainData.projectList && this.mainData.projectList.length > 0) {
                //校验合同含税金额（人民币）===项目分配额合计
                let sumMoney = 0;
                for (let i = 0; i < this.mainData.projectList.length; i++) {
                  const item = this.mainData.projectList[i];
                  sumMoney += item.projectThisChangeMoney === null ? 0 : item.projectThisChangeMoney;
                }
                if ( sumMoney !== this.mainData.thisChangeMoney) {
                  this.$message({
                    showClose: true,
                    message: '金额与项目分配额合计必须相等！',
                    type: 'warning',
                  });
                  return;
                }
              }
            }
            if (this.mainData.dataStateCode === 1 || this.mainData.dataStateCode === 4) {
              if(!bool){
                //生效状态
                this.mainData.takeEffectCode = 2;
                this.mainData.takeEffectName = '待生效';
                //流程状态
                this.mainData.dataState = '审批完成';
                this.mainData.dataStateCode = 5;
                this.save().then((response) => {
                }).then(() => {
                  this.mcpLayout.closeTab();
                });
              }
            }
          } else {
            this.$nextTick(function () {
              document.querySelector('.is-error').scrollIntoView(false);
            });
          }
        });
      },
      getOurPosition() {
        ourApi.query({contractTypeCode: this.mainData.contractTypeCode}).then(res => {
          res.data.data.records.forEach(item => {
            this.ourPositionData.push({
              dicName: item.ourPosition,
              id: item.id
            })
          })
        })
      },
      uploadFiles() {
        this.uploadDialogVisible = true
      },

    },
	};
</script>

<style>
	.mylabel .el-form-item .el-form-item__label {
		line-height: 15px;
	}
</style>
