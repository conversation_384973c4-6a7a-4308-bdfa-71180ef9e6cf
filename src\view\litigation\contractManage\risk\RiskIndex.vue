﻿<template>
  <el-container class="container-manage-sg" direction="vertical">
    <el-header>
      <el-card>
        <div>
          <el-input
              v-model="selectData.fuzzyValue"
              class="filter_input"
              clearable
              placeholder="检索字段（合同名称、合同编码、合同类型、合同金额、对方签约主体）"
              @clear="search_"
              @keyup.enter.native="search_">
            <el-popover slot="prepend" placement="bottom-start" popper-class="popoverStyle" trigger="click"
                        width="850">
              <el-form ref="queryForm" label-width="100px" size="mini">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="合同名称">
                      <el-input v-model="selectData.contractName" clearable placeholder="请输入..."/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="合同类型">
                      <el-input v-model="selectData.contractType" clearable placeholder="请输入..."/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="合同金额">
                      <el-input-number v-model="selectData.contractMoneyMin" :controls="false"
                                       :precision="2" clearable placeholder="最小值" style="width: 45%;float: left;"
                                       @focus="utils.inputFocus"/>
                      <div class="label_1" style="width: 10%;float: left;text-align: center;">
                        <span>至</span>
                      </div>
                      <el-input-number v-model="selectData.contractMoneyMax" :controls="false" :precision="2"
                                       clearable placeholder="最大值" style="width: 45%" @focus="utils.inputFocus"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="合同性质">
                      <el-select v-model="selectData.dataTypeName" clearable style="width: 100%;">
                        <el-option v-for="item in utils.contractNatureData" :key="item.id" :label="item.dicName"
                                   :value="item.dicName"/>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="金额类型">
                      <el-select v-model="selectData.moneyTypeCode" clearable style="width: 100%;">
                        <el-option v-for="item in utils.moneyTypeData" :key="item.dicCode" :label="item.dicName"
                                   :value="item.dicCode"/>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="经办人">
                      <el-input v-model="selectData.createPsnName" clearable placeholder="请输入..."/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="对方签约主体">
                      <el-input v-model="selectData.otherPartyName" clearable placeholder="请输入..."/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="我方签约主体">
                      <el-input v-model="selectData.ourPartyName" clearable placeholder="请输入..."/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="经办时间">
                      <el-date-picker v-model="selectData.startTimeMin" placeholder="选择日期"
                                      style="width: 45%; float: left"
                                      type="date"/>
                      <div class="label_1" style="width: 10%; float: left; text-align: center">
                        <span>至</span>
                      </div>
                      <el-date-picker v-model="selectData.startTimeMax" placeholder="选择日期" style="width: 45%"
                                      type="date"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="生效状态">
                      <el-select v-model="selectData.takeEffectName" clearable style="width: 100%;">
                        <el-option v-for="item in utils.takeEffectData" :key="item.id" :label="item.dicName"
                                   :value="item.dicName"/>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="承办人">
                      <el-input v-model="selectData.riskPsn" clearable placeholder="请输入..."/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="流程状态">
                      <el-select v-model="selectData.dataState" clearable style="width: 100%;">
                        <el-option v-for="item in utils.dataState_BPM_data" :key="item.code" :label="item.name"
                                   :value="item.name"/>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-button-group style="float: right">
                  <el-button icon="el-icon-search" size="mini" type="primary" @click="search_">搜索</el-button>
                  <el-button icon="el-icon-refresh-left" size="mini" type="primary" @click="empty_">重置</el-button>
                </el-button-group>
              </el-form>
              <el-button slot="reference" size="small" type="primary">高级检索</el-button>
              <el-dialog :close-on-click-modal="false" :visible.sync="orgVisible" title="选择单位" width="50%">
                <div class="el-dialog-div">
                  <orgTree
                      :accordion="false"
                      :checked-data.sync="zxcheckedData"
                      :is-check="false"
                      :is-checked-user="isCheckedUser"
                      :is-filter="true"
                      :is-not-cascade="true"
                      :show-user="false"/>
                </div>
                <span slot="footer" class="dialog-footer">
									<el-button class="negative-btn" icon="" @click="cancel_">取消</el-button>
									<el-button class="active-btn" icon="" type="primary" @click="choiceDeptSure_">确定 </el-button>
								</span>
              </el-dialog>
              <el-dialog :close-on-click-modal="false" :visible.sync="orgVisible1" title="选择部门" width="50%">
                <div class="el-dialog-div">
                  <orgTree
                      :accordion="false"
                      :checked-data.sync="zxcheckedData1"
                      :is-check="false"
                      :is-checked-user="isCheckedUser1"
                      :is-filter="true"
                      :is-not-cascade="true"
                      :otherHeight="280"
                      :show-user="false"/>
                </div>
                <span slot="footer" class="dialog-footer">
									<el-button class="negative-btn" icon="" @click="cancel_1">取消</el-button>
									<el-button class="active-btn" icon="" type="primary" @click="choiceDeptSure_1">确定 </el-button>
								</span>
              </el-dialog>
            </el-popover>
            <el-button slot="append" icon="el-icon-search" @click="search_"/>
          </el-input>
        </div>
      </el-card>
    </el-header>
    <el-main>
      <SimpleBoardIndex :hasAdd="true" :title="'风控清单列表'">
        <template slot="button">
          <el-button class="normal-btn" size="mini" type="primary" @click="exportExcel">导出Excel</el-button>
        </template>
        <el-table id="contractTableList" ref="table" :data="tableData" :height="table_height"
                  :show-overflow-tooltip="true"
                  border fit highlight-current-row stripe style="width: 100%"
                  @sort-change="tableSort"
                  @row-dblclick="rowDblclick"
                  @selection-change="handleSelectionChange">
          <el-table-column align="center" label="序号" type="index" width="50"/>
          <el-table-column label="合同编码" min-width="200" prop="contractCode"
                           show-overflow-tooltip/>
          <el-table-column label="版本号" min-width="60" prop="version"
                           show-overflow-tooltip/>
          <el-table-column label="合同名称" min-width="300" prop="contractName"
                           show-overflow-tooltip/>
          <el-table-column label="合同性质" min-width="100" prop="dataTypeName" show-overflow-tooltip
                           sortable="custom"/>
          <el-table-column label="合同类型" min-width="200" prop="contractType"
                           show-overflow-tooltip/>
          <el-table-column label="合同金额" prop="contractMoney" show-overflow-tooltip sortable="custom" width="100">
            <template slot-scope="scope">
              <span v-if="scope.row.dataTypeCode === 1 || scope.row.dataTypeCode === 4 ">
                {{ scope.row.contractMoney }}
              </span>
              <span v-else-if="scope.row.dataTypeCode === 2 || scope.row.dataTypeCode === 5 ">
                {{ scope.row.thisChangeMoney }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="我方签约主体" min-width="200" prop="ourPartyName" show-overflow-tooltip/>
          <el-table-column label="对方签约主体" min-width="200" prop="otherPartyName" show-overflow-tooltip/>
          <el-table-column label="经办人" min-width="100" prop="createPsnName" show-overflow-tooltip/>
          <el-table-column label="经办单位" min-width="200" prop="createOgnName" show-overflow-tooltip/>
          <el-table-column label="经办时间" min-width="100" prop="createTime" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.createTime | parseTime('{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="承办人" min-width="100" prop="riskPsn" show-overflow-tooltip/>
          <el-table-column label="承办时间" min-width="100" prop="undertakingTime" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.undertakingTime | parseTime('{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="考核分数" min-width="100" prop="assessmentScore" show-overflow-tooltip/>
          <el-table-column fixed="right" label="流程状态" min-width="100" prop="dataState" show-overflow-tooltip
                           sortable="custom"/>
        </el-table>
      </SimpleBoardIndex>
    </el-main>
    <el-footer>
      <pagination
          :limit.sync="selectData.limit"
          :page.sync="selectData.page"
          :total="selectData.total"
          @pagination="refreshData"
      />
    </el-footer>
  </el-container>
</template>

<script>
import {mapGetters} from "vuex"
import taskApi from '@/api/_system/task'
import contractApi from '@/api/contract/bmContract'
import pagination from '../../../components/Pagination/PaginationIndex'
import SimpleBoardIndex from "@/view/components/SimpleBoard/SimpleBoardIndex"
import XLSX from 'xlsx';
import FileSaver from 'file-saver';
import orgTree from '@/view/components/OrgTree/OrgTree';

export default {
  name: 'riskIndex',
  inject: ['layout'],
  components: {pagination, SimpleBoardIndex, orgTree},
  computed: {
    ...mapGetters(["orgContext"]),
  },
  data() {
    return {
      multipleContract: [],
      otherHeight: 0,
      isCheckedUser: false,
      orgVisible: false,
      isCheckedUser1: false,
      orgVisible1: false,
      zxcheckedData: [],
      zxcheckedData1: [],
      selectData: {
        contractName: null,
        contractMoneyMin: null,
        contractMoneyMax: null,
        dataTypeName: null,
        moneyTypeCode: null,
        revenueExpenditureCode: null,
        otherPartyName: null,
        ourPartyName: null,
        isAtemplate: null,
        projectNames: null,
        contractCode: null,
        customCode: null,
        whetherGroupMajor: null,
        dataSource: null,
        createPsnName: null,
        startTimeMin: null,
        startTimeMax: null,
        createDeptName: null,
        createOgnName: null,
        dataState: null,
        takeEffectName: null,
        closeState: null,
        changeState: null,
        fuzzyValue: null,
        page: 1,
        limit: 10,
        total: 0,
        sortName: null,
        order: false,
        orgId: null,
        isQuery: true,
        functionType: 'riskIndex'
      },
      table_height: '100%',
      tableData: [],
    }
  },
  created() {
    this.refreshData()
  },
  activated() {
    this.refreshData()
  },
  mounted() {
    this.$nextTick(function () {
      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 45
      // 监听窗口大小变化
      const self = this
      window.onresize = function () {
        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 45
        self.otherHeight = self.table_height
      }
      this.otherHeight = this.table_height
    })
  },
  methods: {
    selectRow(row, index) {
      if (row.takeEffectCode == '3') {
        return true;
      }
      return false;
    },
    handleSelectionChange(val) {
      this.multipleContract = val;
      console.log(val);
    },
    contractView(row) {
      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataSourceCode !== "100008") {
        const tabId = this.utils.createUUID();
        this.layout.openNewTab(
            "合同审批信息",
            "contract_approval_main_detail",
            "contract_approval_main_detail",
            tabId,
            {
              functionId: "contract_approval_main_detail," + tabId,
              ...this.utils.routeState.VIEW(row.id)
            }
        )
      } else {
        taskApi.selectTaskId({businessKey: row.id, isView: 'true'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("合同审批信息",
              "design_page",
              "design_page",
              tabId,
              {
                processInstanceId: res.data.data[0].PID,//流程实例
                taskId: res.data.data[0].ID,//任务ID
                businessKey: row.id, //业务数据ID
                functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                entranceType: "FLOWABLE",
                type: "haveDealt",
                channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
                view: 'new',
                isRisk: '是',
              }
          )
        })
      }
    },
    changeView(row) {
      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataSourceCode !== "100008") {
        const tabId = this.utils.createUUID();
        this.layout.openNewTab(
            "合同变更",
            "contract_change_main_detail",
            "contract_change_main_detail",
            tabId,
            {
              functionId: "contract_change_main_detail," + tabId,
              ...this.utils.routeState.VIEW(row.id)
            }
        )
      } else {
        taskApi.selectTaskId({businessKey: row.id, isView: 'true'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("合同审批信息",
              "design_page",
              "design_page",
              tabId,
              {
                processInstanceId: res.data.data[0].PID,//流程实例
                taskId: res.data.data[0].ID,//任务ID
                businessKey: row.id, //业务数据ID
                functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                entranceType: "FLOWABLE",
                type: "haveDealt",
                channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
                view: 'new',
                isRisk: '是',
              }
          )
        })
      }
    },
    stopView(row) {
      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataSourceCode !== "100008") {
        const tabId = this.utils.createUUID();
        this.layout.openNewTab(
            "合同终止协议",
            "contract_stop_main_detail",
            "contract_stop_main_detail",
            tabId,
            {
              functionId: "contract_stop_main_detail," + tabId,
              ...this.utils.routeState.VIEW(row.id)
            }
        )
      } else {
        taskApi.selectTaskId({businessKey: row.id, isView: 'true'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("合同审批信息",
              "design_page",
              "design_page",
              tabId,
              {
                processInstanceId: res.data.data[0].PID,//流程实例
                taskId: res.data.data[0].ID,//任务ID
                businessKey: row.id, //业务数据ID
                functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                entranceType: "FLOWABLE",
                type: "haveDealt",
                channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
                view: 'new',
              }
          )
        })
      }
    },
    moreView(row) {
      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code) {
        const tabId = this.utils.createUUID();
        this.layout.openNewTab(
            "合同合并审批",
            "contract_more_main_detail",
            "contract_more_main_detail",
            tabId,
            {
              functionId: "contract_more_main_detail," + tabId,
              ...this.utils.routeState.VIEW(row.parentId)
            }
        )
      } else {
        taskApi.selectTaskId({businessKey: row.parentId, isView: 'true'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("合同合并审批",
              "design_page",
              "design_page",
              tabId,
              {
                processInstanceId: res.data.data[0].PID,//流程实例
                taskId: res.data.data[0].ID,//任务ID
                businessKey: row.parentId, //业务数据ID
                functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                entranceType: "FLOWABLE",
                type: "haveDealt",
                channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
                view: 'new',
                isRisk: '是',
              }
          )
        })
      }
    },
    // 刷新或查询数据
    refreshData() {
      this.selectData.orgId = this.orgContext.currentOrgId
      contractApi.queryRisk(this.selectData).then(response => {
        this.tableData = response.data.data.records
        this.selectData.total = response.data.data.total
      })
    },
    // 搜索
    search_: function () {
      this.selectData.page = 1
      this.refreshData()
    },
    // 清空
    empty_() {
      this.selectData = {
        contractName: null,
        contractMoneyMin: null,
        contractMoneyMax: null,
        dataTypeName: null,
        moneyTypeCode: null,
        revenueExpenditureCode: null,
        otherPartyName: null,
        ourPartyName: null,
        isAtemplate: null,
        projectNames: null,
        contractCode: null,
        customCode: null,
        whetherGroupMajor: null,
        dataSource: null,
        createPsnName: null,
        startTimeMin: null,
        startTimeMax: null,
        createDeptName: null,
        createOgnName: null,
        dataState: null,
        takeEffectName: null,
        closeState: null,
        changeState: null,
        fuzzyValue: null,
        page: 1,
        limit: 10,
        total: 0,
        sortName: null,
        order: false,
        isQuery: true,
        functionType: 'contractLedger'
      }
      this.refreshData()
    },
    // 刷新
    refresh_() {
      this.selectData.contractName = ''// 合同名称
      this.selectData.contractTypeName = ''// 合同类型
      this.selectData.fuzzySearch = ''// 模糊搜索
      this.selectData.sortName = null
      this.selectData.order = false
      this.refreshData()
    },
    // 双击查看
    rowDblclick(row, column, event) {
      /*合并审批*/
      if (row.mergeStateCode === 3 || row.mergeStateCode === 5) {
        this.moreView(row)
        return
      }
      /*合同*/
      if (row.dataTypeCode === this.utils.contractNature.main.id) {
        this.contractView(row)
        return
      }
      /*合同变更*/
      if (row.dataTypeCode === this.utils.contractNature.change.id) {
        this.changeView(row)
        return
      }
      /*终止协议*/
      if (row.dataTypeCode === this.utils.contractNature.stop.id) {
        this.stopView(row)
        return
      }
    },
    tableSort(column, prop, order) {
      this.selectData.sortName = column.prop
      this.selectData.order = column.order === "ascending"
      this.refreshData()
    },
    // exportExcel() {
    //   var xlsxParam = {raw: true}; // 导出的内容只做解析，不进行格式转换
    //   // let fix = document.querySelector(".el-table__fixed");//如果是都给了fixed样式
    //   let fix = document.querySelector('.el-table__fixed-right'); //如果是只有右边有fixed样式
    //   let wb;
    //   if (fix) {
    //     //判断要导出的节点中是否有fixed的表格，如果有，转换excel时先将该dom移除，然后append回去
    //     wb = XLSX.utils.table_to_book(document.querySelector('#contractTableList').removeChild(fix), xlsxParam);
    //     document.querySelector('#contractTableList').appendChild(fix);
    //   } else {
    //     wb = XLSX.utils.table_to_book(document.querySelector('#contractTableList'), xlsxParam);
    //   }
    //   var wbout = XLSX.write(wb, {
    //     bookType: 'xlsx',
    //     bookSST: true,
    //     type: 'array',
    //   });
    //   try {
    //     FileSaver.saveAs(new Blob([wbout], {type: 'application/octet-stream'}), '风控清单列表.xlsx');
    //   } catch (e) {
    //     if (typeof console !== 'undefined') {
    //     }
    //   }
    //   return wbout;
    // },
    exportExcel() {
      this.exportLoading = true;

      // 显示提示消息
      const loadingMessage = this.$message({
        message: '正在准备导出数据，请稍候...',
        type: 'info',
        duration: 0
      });

      const queryParams = {};
      Object.assign(queryParams, this.selectData);
      queryParams.limit = 9999;

      let date = new Date();
      let formatDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

      contractApi.exportRiskLedger(queryParams).then((response) => {
        const blob = response.data;
        const fileName = formatDate + '风控清单台账.xlsx';

        // 关闭提示消息
        loadingMessage.close();

        // 下载文件
        if ('download' in document.createElement('a')) {
          const elink = document.createElement('a');
          elink.download = fileName;
          elink.style.display = 'none';
          elink.href = URL.createObjectURL(blob);
          document.body.appendChild(elink);
          elink.click();
          URL.revokeObjectURL(elink.href);
          document.body.removeChild(elink);

          this.$message.success('导出成功');
        } else {
          navigator.msSaveBlob(blob, fileName);
        }
      }).catch(error => {
        this.$message.error('导出失败：' + (error.message || '未知错误'));
      }).finally(() => {
        this.exportLoading = false;
      });
    },
    choiceDeptSure_() {
      let c = '';
      let cid = '';
      const me = this;
      const res = this.zxcheckedData[0];

      if (res.unitType !== 'ogn' && res.unitType !== 'branch') {
        this.$message.error('请选择法人单位或分公司');
      } else {
        this.zxcheckedData.forEach((item) => {
          if (c.length === 0) {
            c = c + item.name;
            cid = cid + item.unitId;
          } else {
            c = c + ',' + item.name;
            cid = cid + ',' + item.unitId;
          }
        });

        this.selectData.createOgnName = c;
        // orgApi.queryMainDataOrgIds({ids: cid}).then((res) => {
        //   me.selectData.createOgnId = res.data.msg;
        // });
        this.orgVisible = false;
      }
    },
    choiceDeptSure_1() {
      let c = '';
      let cid = '';
      const me = this;
      const res = this.zxcheckedData1[0];

      if (res.unitType !== 'dept') {
        this.$message.error('请选择部门');
      } else {
        this.zxcheckedData1.forEach((item) => {
          if (c.length === 0) {
            c = c + item.name;
            cid = cid + item.unitId;
          } else {
            c = c + ',' + item.name;
            cid = cid + ',' + item.unitId;
          }
        });

        this.selectData.createDeptName = c;
        this.selectData.createDeptId = cid;
        this.orgVisible1 = false;
      }
    },
    cancel_() {
      this.orgVisible = false;
    },
    cancel_1() {
      this.orgVisible1 = false;
    },
  }
}
</script>

<style scoped>
.el-table__fixed-body-wrapper {
  top: 50px !important;
}

.filter_input .el-input-group__prepend {
  color: #FFFFFF;
  background-color: #1890ff;
  border-color: #1890ff;
}

.label_1 {
  text-align: right;
  padding-right: 5px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}

.myNew .el-input__inner {
  background: #1F95FF;
}

.myNew .el-input__suffix {
  color: white;
}

.myNew input::-webkit-input-placeholder {
  color: white;
}

.mylabel .el-form-item--mini .el-form-item__label {
  line-height: 15px;
}

.scrollClass {
  /* margin-top: 10px; */
  height: 50vh;
  //width: 100%;
  //overflow-y: scroll;
}

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden !important;
}

/deep/ .el-scrollbar__wrap {
  overflow-x: hidden !important;
}

/* 树形结构添加横向滚动条 */
::v-deep .el-tree > .el-tree-node {
  width: 120%;
  display: inline-block;
}

/deep/ .el-tree > .el-tree-node {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.el-row {
  display: flex;
  flex-wrap: wrap;
}

.el-dialog {
  width: 100% !important;
  height: 70vh;
  overflow: auto;
  box-shadow: 0 0px 0px rgba(0, 0, 0, 0) !important;
  margin: 0 auto 0px !important;
}
</style>
<style lang="scss">
.el-popover.popoverStyle {
  height: 300px;
  overflow: auto;
}
</style>
